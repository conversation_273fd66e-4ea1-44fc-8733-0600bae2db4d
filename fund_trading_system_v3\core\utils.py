"""
核心工具函数和通用导入
包含系统中使用的通用工具函数和导入语句
"""

import os
import sys
import time
import random
import logging
import numpy as np
import pandas as pd
import talib as ta
import json
import warnings
from datetime import datetime, timedelta
from typing import Dict, List, Union, Optional, Tuple, Any, Callable
from collections import OrderedDict, deque
from abc import ABC, abstractmethod
from scipy.stats import norm
from concurrent.futures import ThreadPoolExecutor, as_completed

# 忽略警告
warnings.filterwarnings('ignore')

# 导入真实数据分析模块
try:
    from czsc_func import get_kline, get_realtime_quote
    from enhanced_czsc_func import (
        get_enhanced_technical_indicators,
        calculate_real_fund_flow_strength,
        calculate_market_sentiment_indicators,
        calculate_multi_timeframe_coordination,
        get_real_confidence_metrics,
        analyze_real_gua_from_price_action,
        get_volume_profile_analysis,
        calculate_trend_strength_metrics,
        validate_data_quality
    )
    CZSC_FUNC_AVAILABLE = True
except ImportError:
    CZSC_FUNC_AVAILABLE = False
    logging.warning("CZSC functions not available")

# 导入puppet交易库
try:
    import puppet
    PUPPET_AVAILABLE = True
except ImportError:
    PUPPET_AVAILABLE = False
    logging.warning("Puppet trading library not available")

# 导入CZSC库
try:
    from czsc import CZSC, Freq, RawBar as CzscRawBar
    from czsc.analyze import BI, FX
    CZSC_AVAILABLE = True
except ImportError:
    CZSC_AVAILABLE = False
    logging.warning("CZSC library not available")

# 检查是否有增强版功能
try:
    from czsc.utils.corr import cross_correlation  # 测试特定功能
    CZSC_ENHANCED_AVAILABLE = True
except ImportError:
    CZSC_ENHANCED_AVAILABLE = False
