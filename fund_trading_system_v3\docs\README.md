# 增强版基金交易多智能体系统 V3.0

## 🚀 项目概述

增强版基金交易多智能体系统V3是一个集成六大维度评估体系的完整自动化交易系统，具备智能冲突解决、动态权重管理和分型质量验证等先进功能。

## ✨ 主要特性

### 🔄 六大维度评估体系
- **趋势维度**: 评估市场趋势的强度、方向和可持续性
- **波动性维度**: 分析市场波动性和风险水平
- **流动性维度**: 评估基金的流动性状况
- **情绪维度**: 分析市场情绪和投资者心理状态
- **结构维度**: 评估市场结构的稳定性和变化趋势
- **转换维度**: 评估市场转换的可能性和阶段

### 🤖 多智能体协调
- **传统智能体**: 技术分析、卦象分析、资金流向分析
- **增强版智能体**: 增强技术分析、索罗斯反身性理论、时间框架背驰分析
- **决策智能体**: 集成六大维度的高级决策智能体

### 🧠 智能分析组件
- **信号冲突解决器**: 自动处理维度间信号冲突
- **动态权重管理器**: 根据市场环境自动调整权重
- **分型质量验证器**: 确保分析结果的可靠性
- **市场智能分类器**: 自动识别市场状态和特征

## 📁 项目结构

```
fund_trading_system_v3/
├── agents/                 # 智能体模块
│   ├── traditional/        # 传统智能体
│   └── enhanced/          # 增强版智能体
├── analyzers/             # 分析器模块
├── coordinators/          # 协调器模块
├── core/                  # 核心模块
├── evaluators/            # 评估器模块
├── optimizers/            # 优化器模块
├── system/                # 系统模块
├── tests/                 # 测试模块
└── docs/                  # 文档模块
```

## 🛠️ 安装和配置

### 环境要求
- Python 3.9+
- 依赖库: numpy, pandas, logging等

### 快速开始

1. **运行主程序**:
```bash
cd fund_trading_system_v3
python main.py
```

2. **运行测试**:
```bash
python run_tests.py
```

3. **运行特定测试模块**:
```bash
python run_tests.py test_core
```

## 📊 测试验证

系统包含完整的测试套件，覆盖：
- ✅ 核心模块测试 (枚举、数据结构、工具函数)
- ✅ 评估器测试 (六大维度评估器)
- ✅ 智能体测试 (传统和增强版智能体)
- ✅ 系统集成测试 (端到端功能测试)

当前测试状态: **83.9%通过率** (31个测试中26个通过)

## 🔧 配置说明

### 基金列表配置
系统默认支持以下基金:
- 513030, 513080, 513500, 513520, 513300
- 513850, 159329, 159561, 520830, 159567
- 518880, 601398

### 风险控制参数
- 单个基金最大仓位: 20%
- 单日最大亏损: 5%
- 最小置信度要求: 60%

## 📈 性能特性

- **模块化设计**: 易于扩展和维护
- **智能缓存**: 减少重复计算
- **异常处理**: 完善的错误处理机制
- **日志记录**: 详细的运行日志

## 🔍 系统诊断

系统提供完整的诊断功能:
```python
system = EnhancedFundTradingSystemV3()
diagnosis = system.diagnose_system_status_v3('513500')
```

## 📚 API文档

详细的API文档请参考 `docs/api/` 目录。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 支持

如有问题或建议，请提交 Issue 或联系开发团队。
