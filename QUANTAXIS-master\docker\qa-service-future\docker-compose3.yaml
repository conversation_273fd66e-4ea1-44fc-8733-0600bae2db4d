version: "3.7"
services:
    qa:
        image: daocloud.io/quantaxis/qacommunity:latest
        container_name: qacommunity
        depends_on:
            - mgdb
            - qaeventmq
        networks:
            qanetwork:
                ipv4_address: **********
        ports:
            - "8888:8888"
            - "81:80"
        env_file: env.env
        volumes:
            - qacode:/home
        restart: always

    # qacron:
    #     image: barretthugh/qa-cron
    #     environment:
    #         - MONGODB=mgdb
    #     restart: always

    qaweb_run:
        image: daocloud.io/quantaxis/qarun:latest
        container_name: qarun
        networks:
            qanetwork:
                ipv4_address: **********
        depends_on:
            - mgdb
            - qaeventmq
        ports:
            - "8010:8010"
        env_file: env.env
        restart: always
        depends_on:
          - qaeventmq
          - mgdb
        command: ['/root/wait_for_it.sh', 'qaeventmq:15672', '--' , "/root/runcelery.sh"]

    mgdb:
        image: daocloud.io/quantaxis/qamongo_single:latest
        ports:
            - "27017:27017"
        env_file: env.env
        volumes:
            - qamg:/data/db
        networks:
            qanetwork:
                ipv4_address: **********
        restart: always

    qaeventmq:
        image: daocloud.io/quantaxis/qaeventmq:latest
        ports:
            - "15672:15672"
            - "5672:5672"
            - "4369:4369"
        env_file: env.env
        networks:
            qanetwork:
                ipv4_address: **********
        restart: always

    qamonitor:
        image: daocloud.io/quantaxis/qa-monitor:latest
        ports:
            - "61209:61209"
            - "61208:61208"
        pid: "host"
        networks:
            qanetwork:
                ipv4_address: **********

        restart: always

    qactpbeebroker:
        image: daocloud.io/quantaxis/qactpbeebroker:latest
        ports:
            - "5000:5000"
        networks:
            qanetwork:
                ipv4_address: **********
        env_file: env.env
        command: ['/root/wait_for_it.sh', 'qaeventmq:15672', '--' , "QACTPBEE", "--userid", "$${CTPBEEuserid}", "--password", '$${CTPBEEpassword}']

    qamarketcollector:
        image: daocloud.io/quantaxis/qarealtimecollector:latest
        ports:
            - "8011:8011"
        depends_on:
            - mgdb
            - qaeventmq
        env_file: env.env
        networks:
            qanetwork:
                ipv4_address: **********
        command:
            ['/root/QUANTAXIS_RealtimeCollector/docker/wait_for_it.sh', 'qaeventmq:5672', '--' ,'/root/QUANTAXIS_RealtimeCollector/docker/start_collector.sh']


    qatrader:
        image: daocloud.io/quantaxis/qatrader:latest
        ports:
            - "8020:8020"
        depends_on:
            - mgdb
            - qaeventmq
        env_file: env.env
        command:
            ['/root/QATrader/docker/wait_for_it.sh', 'qaeventmq:5672', '--' ,'qatraderserver']

        networks:
            qanetwork:
                ipv4_address: **********



volumes:
    qamg:
        external:
            name: qamg
    qacode:
        external:
            name: qacode
networks:
    qanetwork:
        ipam:
            config:
                - subnet: **********/24
