version: "2.1"
services:

  redis:
    image: 'daocloud.io/quantaxis/qaredis:latest'
    ports:
      - "6379:6379"
    environment:
      - TZ=Asia/Shanghai
    command: ['redis-server']
    restart: always
    networks:
      qanetwork_db:
        ipv4_address: ***********
  mgdb:
    image: daocloud.io/quantaxis/qamongo_single:latest
    ports:
      - "27017:27017"
    environment:
      - TZ=Asia/Shanghai
      - MONGO_INITDB_DATABASE=quantaxis
    volumes:
      - qamg:/data/db
    networks:
      qanetwork_db:
        ipv4_address: ***********
    restart: always

  qaclickhouse:
    image: daocloud.io/quantaxis/qa-clickhouse
    ports:
      - "9000:9000"
      - "8123:8123"
      - "9009:9009"
    environment:
      - TZ=Asia/Shanghai
    networks:
      qanetwork_db:
        ipv4_address: ***********

  qaeventmq:
    image: daocloud.io/quantaxis/qaeventmq:latest
    ports:
      - "15672:15672"
      - "5672:5672"
      - "4369:4369"
    environment:
      - TZ=Asia/Shanghai
    networks:
      qanetwork_db:
        ipv4_address: ***********
    restart: always


volumes:
  qamg:
    external:
      name: qamg

networks:
  qanetwork_db:
    ipam:
      config:
        - subnet: ***********/24
          gateway: ***********
