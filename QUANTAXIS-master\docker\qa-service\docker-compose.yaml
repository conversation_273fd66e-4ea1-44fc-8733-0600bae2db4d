version: '2'
services:
    qa:
        image: daocloud.io/quantaxis/qacommunity-rust-go:allin-20210218
        container_name: qacommunity-rust
        depends_on:
            - mgdb
            - qaeventmq
        networks:
            qanetwork:
                ipv4_address: **********
        ports:
            - "8888:8888"
            - "81:80"
            - "8787:8787"
        environment:
          - TZ=Asia/Shanghai
          - LANG=C.UTF-8
          - MONGODB=mgdb
          - QARUN=qaweb
          - QAPUBSUB_IP=qaeventmq
          - QAPUBSUB_PORT=5672
          - QAPUBSUB_USER=admin
          - QAPUBSUB_PWD=admin
        volumes:
            - qacode:/root/code
        restart: always


    qaweb:
        image: daocloud.io/quantaxis/qacommunity-rust-go:allin-20210218
        container_name: qaweb
        depends_on:
            - mgdb
            - qaeventmq
        networks:
            qanetwork:
                ipv4_address: **********
        ports:
            - "8010:8010"
            - "8018:8018"
            - "8019:8019"
            - "8028:8028"
            - "8029:8029"
        environment:
          - MONGODB=mgdb
          - QAPUBSUB_IP=qaeventmq
          - QAPUBSUB_PORT=5672
          - QAPUBSUB_USER=admin
          - QAPUBSUB_PWD=admin
          - QARUN_AMQP=pyamqp://admin:admin@qaeventmq:5672//
          - TZ=Asia/Shanghai
        restart: always
        command: ['/root/wait_for_it.sh', 'qaeventmq:5672', '--' , "/root/runcelery.sh"]


    # qacron:
    #     image: daocloud.io/quantaxis/qa-cron:latest
    #     environment:
    #         - MONGODB=mgdb
    #     restart: always


    mgdb:
        image: daocloud.io/quantaxis/qamongo_single:latest
        ports:
            - "27017:27017"
        environment:
            - TZ=Asia/Shanghai
            - MONGO_INITDB_DATABASE=quantaxis
        volumes:
            - qamg:/data/db
        networks:
            qanetwork:
                ipv4_address: **********
        restart: always


    qaeventmq:
        image: daocloud.io/quantaxis/qaeventmq:latest
        ports:
            - "15672:15672"
            - "5672:5672"
            - "4369:4369"
        environment:
            - TZ=Asia/Shanghai
        networks:
            qanetwork:
                ipv4_address: **********
        restart: always


    qatrader:
        image: daocloud.io/quantaxis/qatrader:latest
        ports:
            - "8020:8020"
        depends_on:
            - mgdb
            - qaeventmq
        environment:
            - MONGODB=mgdb
            - QAPUBSUB_IP=qaeventmq
            - QAPUBSUB_PORT=5672
            - QAPUBSUB_USER=admin
            - QAPUBSUB_PWD=admin
            - QARUN_AMQP=pyamqp://admin:admin@qaeventmq:5672//
            - TZ=Asia/Shanghai
        command:
            ['/root/QATrader/docker/wait_for_it.sh', 'qaeventmq:5672', '--' ,'qatraderserver']

        networks:
            qanetwork:
                ipv4_address: **********


volumes:
    qamg:
        external:
            name: qamg
    qacode:
        external:
            name: qacode
networks:
    qanetwork:
        ipam:
            config:
            - subnet: **********/24
              gateway: **********
