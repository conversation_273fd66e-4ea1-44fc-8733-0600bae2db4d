apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: E:\k8s-for-docker-desktop\kompose-windows-amd64.exe convert --replication-controller
      --replicas 3
    kompose.version: 1.18.0 (06a2e56)
  creationTimestamp: null
  labels:
    io.kompose.service: qaweb-run
  name: qaweb-run
spec:
  ports:
  - name: "8010"
    port: 8010
    targetPort: 8010
  selector:
    io.kompose.service: qaweb-run
status:
  loadBalancer: {}
