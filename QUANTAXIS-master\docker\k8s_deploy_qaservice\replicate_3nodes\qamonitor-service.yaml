apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: E:\k8s-for-docker-desktop\kompose-windows-amd64.exe convert --replication-controller
      --replicas 3
    kompose.version: 1.18.0 (06a2e56)
  creationTimestamp: null
  labels:
    io.kompose.service: qamonitor
  name: qamonitor
spec:
  ports:
  - name: "61209"
    port: 61209
    targetPort: 61209
  - name: "61208"
    port: 61208
    targetPort: 61208
  selector:
    io.kompose.service: qamonitor
status:
  loadBalancer: {}
