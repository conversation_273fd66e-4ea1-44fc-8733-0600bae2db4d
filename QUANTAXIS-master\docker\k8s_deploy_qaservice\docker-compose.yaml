version: '2'
services:
    qa:
        image: daocloud.io/quantaxis/qacommunity:latest
        container_name: qacommunity
        depends_on:
            - mgdb
            - qaeventmq
        networks: 
            qanetwork:
                ipv4_address: **********
        ports:
            - "8888:8888"
            - "81:80"
        environment:
          - TZ=Asia/Shanghai
          - MONGODB=mgdb
          - QARUN=qaweb
          - QAPUBSUB_IP=qaeventmq
          - QAPUBSUB_PORT=5672
          - QAPUBSUB_USER=admin
          - QAPUBSUB_PWD=admin
        volumes:
            - qacode:/home
        restart: always

    # qacron:
    #     image: daocloud.io/quantaxis/qa-cron:latest
    #     environment:
    #         - MONGODB=mgdb
    #     restart: always

    qaweb_run:
        image: daocloud.io/quantaxis/qarun:latest
        container_name: qarun
        depends_on:
            - mgdb
            - qaeventmq
        networks: 
            qanetwork:
                ipv4_address: **********
        ports:
            - "8010:8010"
        environment:
          - MONGODB=mgdb
          - QAPUBSUB_IP=qaeventmq
          - QAPUBSUB_PORT=5672
          - QAPUBSUB_USER=admin
          - QAPUBSUB_PWD=admin
          - QARUN_AMQP=pyamqp://admin:admin@qaeventmq:5672//
          - TZ=Asia/Shanghai
        restart: always
        depends_on:
          - qaeventmq
          - mgdb
        command: ['/root/wait_for_it.sh', 'qaeventmq:5672', '--' , "/root/runcelery.sh"]


    mgdb:
        image: daocloud.io/quantaxis/qamongo_single:latest
        ports:
            - "27017:27017"
        environment:
            - TZ=Asia/Shanghai
            - MONGO_INITDB_DATABASE=quantaxis
        volumes:
            - qamg:/data/db
        networks: 
            qanetwork:
                ipv4_address: **********
        restart: always


    qaeventmq:
        image: daocloud.io/quantaxis/qaeventmq:latest
        ports: 
            - "15672:15672"
            - "5672:5672"
            - "4369:4369"
        environment:
            - TZ=Asia/Shanghai
        networks: 
            qanetwork:
                ipv4_address: **********
        restart: always

    qamonitor:
        image: daocloud.io/quantaxis/qa-monitor:latest
        ports:
            - "61209:61209"
            - "61208:61208"
        pid: 'host'
        networks: 
            qanetwork:
                ipv4_address: **********
                
        restart: always
        
    qamarketcollector:
        image: daocloud.io/quantaxis/qarealtimecollector:latest
        ports:
            - "8011:8011"
        depends_on:
            - mgdb
            - qaeventmq
        environment:
            - MONGODB=mgdb
            - EventMQ_IP=qaeventmq
        networks:
            qanetwork:
                ipv4_address: **********
        command:
            ['/root/QUANTAXIS_RealtimeCollector/docker/wait_for_it.sh', 'qaeventmq:5672', '--' ,'/root/QUANTAXIS_RealtimeCollector/docker/start_collector.sh']
volumes:
    qamg:
        external:
            name: qamg
    qacode:
        external:
            name: qacode
networks:
    qanetwork:
        ipam:
            config:
            - subnet: **********/24
              gateway: **********
