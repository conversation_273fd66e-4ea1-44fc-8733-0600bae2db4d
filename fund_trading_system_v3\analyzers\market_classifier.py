"""
多维度市场分类器
基于六大维度评估结果对市场进行智能分类
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from ..core.data_structures import DimensionEvaluationResult


class MultiDimensionalMarketClassifier:
    """
    @class MultiDimensionalMarketClassifier
    @brief 多维度市场分类器
    @details 基于六大维度评估结果对市场进行智能分类
    """
    
    def __init__(self):
        self.name = "MultiDimensionalMarketClassifier"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 市场分类规则
        self.classification_rules = {
            "强势突破": {
                "conditions": {"趋势": (">", 0.7), "结构": (">", 0.6), "流动性": (">", 0.5)},
                "priority": 1
            },
            "趋势确认": {
                "conditions": {"趋势": (">", 0.5), "情绪": (">", 0.3), "波动性": ("<", 0.7)},
                "priority": 2
            },
            "震荡整理": {
                "conditions": {"趋势": ("between", -0.3, 0.3), "波动性": ("<", 0.5)},
                "priority": 3
            },
            "风险警示": {
                "conditions": {"波动性": (">", 0.8), "情绪": ("<", -0.5)},
                "priority": 1
            }
        }
        
    def classify_market(self, evaluations: Dict[str, DimensionEvaluationResult]) -> Dict[str, Any]:
        """
        @brief 对市场进行分类
        @param evaluations: 各维度评估结果
        @return: 市场分类结果
        """
        try:
            # 提取评分
            scores = {dim: eval_result.score for dim, eval_result in evaluations.items()}
            
            # 匹配分类规则
            matched_classifications = []
            for class_name, rule in self.classification_rules.items():
                if self._check_conditions(scores, rule["conditions"]):
                    matched_classifications.append({
                        'classification': class_name,
                        'priority': rule["priority"],
                        'confidence': self._calculate_match_confidence(scores, rule["conditions"])
                    })
            
            # 排序并选择最佳分类
            matched_classifications.sort(key=lambda x: (x['priority'], -x['confidence']))
            
            primary_classification = matched_classifications[0] if matched_classifications else {
                'classification': '未知市场',
                'priority': 999,
                'confidence': 0.1
            }
            
            return {
                'primary_classification': primary_classification['classification'],
                'classification_confidence': primary_classification['confidence'],
                'all_matches': matched_classifications,
                'market_characteristics': self._extract_characteristics(evaluations),
                'classification_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Market classification failed: {str(e)}")
            return {
                'primary_classification': '分类错误',
                'classification_confidence': 0.0,
                'error': str(e)
            }
    
    def _check_conditions(self, scores: Dict[str, float], conditions: Dict[str, tuple]) -> bool:
        """检查分类条件"""
        for dimension, condition in conditions.items():
            if dimension not in scores:
                continue
                
            score = scores[dimension]
            operator, *params = condition
            
            if operator == ">":
                if not (score > params[0]):
                    return False
            elif operator == "<":
                if not (score < params[0]):
                    return False
            elif operator == "between":
                if not (params[0] <= score <= params[1]):
                    return False
        
        return True
    
    def _calculate_match_confidence(self, scores: Dict[str, float], conditions: Dict[str, tuple]) -> float:
        """计算匹配置信度"""
        total_confidence = 0.0
        condition_count = 0
        
        for dimension, condition in conditions.items():
            if dimension not in scores:
                continue
                
            score = scores[dimension]
            operator, *params = condition
            
            if operator == ">":
                confidence = min(1.0, max(0.0, (score - params[0]) / (1.0 - params[0])))
            elif operator == "<":
                confidence = min(1.0, max(0.0, (params[0] - score) / (params[0] + 1.0)))
            elif operator == "between":
                mid_point = (params[0] + params[1]) / 2
                confidence = 1.0 - abs(score - mid_point) / ((params[1] - params[0]) / 2)
            else:
                confidence = 0.5
                
            total_confidence += confidence
            condition_count += 1
        
        return total_confidence / max(1, condition_count)
    
    def _extract_characteristics(self, evaluations: Dict[str, DimensionEvaluationResult]) -> List[str]:
        """提取市场特征"""
        characteristics = []
        
        for dim_name, eval_result in evaluations.items():
            score = eval_result.score
            
            if score >= 0.7:
                characteristics.append(f"{dim_name}强势")
            elif score >= 0.3:
                characteristics.append(f"{dim_name}偏强")
            elif score <= -0.7:
                characteristics.append(f"{dim_name}弱势")
            elif score <= -0.3:
                characteristics.append(f"{dim_name}偏弱")
            else:
                characteristics.append(f"{dim_name}中性")
        
        return characteristics
