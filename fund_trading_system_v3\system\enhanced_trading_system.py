"""
增强版基金交易系统 V3
集成六大维度评估体系的完整自动化交易系统
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
import time
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from coordinators import MultiAgentCoordinatorV3, FundTradingExecutorV3, TradingAgent
from agents.traditional import TechnicalAgent, GuaAnalysisAgent, FundFlowAgent
from agents.enhanced import EnhancedTechnicalAgent, SorosReflexivityAgent
from core.utils import *


class EnhancedFundTradingSystemV3:
    """
    @class EnhancedFundTradingSystemV3
    @brief 增强版基金交易多智能体系统V3
    @details 集成六大维度评估体系的完整自动化交易系统
    """
    
    def __init__(self, title: str = ""):
        # 配置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化所有代理
        self.technical_agent = TechnicalAgent()
        self.enhanced_technical_agent = EnhancedTechnicalAgent()
        self.gua_agent = GuaAnalysisAgent()
        self.fund_flow_agent = FundFlowAgent()
        self.soros_agent = SorosReflexivityAgent()
        self.trading_agent = TradingAgent(title=title)
        
        # 初始化V3新增组件
        self.coordinator = MultiAgentCoordinatorV3()
        self.executor = FundTradingExecutorV3()
        
        # 交易基金列表
        self.buy_fund_list = [
            '513030', '513080', '513500', '513520', '513300', 
            '513850', '159329', '159561', '520830', '159567',
            '518880', '601398'
        ]
        
        # 新闻和指数检查时间段
        self.news_index_time = [
            ['09:27:00', '09:32:00'], ['10:00:00', '10:06:00'], 
            ['10:30:00', '10:36:00'], ['11:00:00', '11:06:00'],
            ['13:00:00', '13:06:00'], ['13:30:00', '13:36:00'], 
            ['14:00:00', '14:06:00'], ['14:30:00', '14:36:00']
        ]
        
        # 并行处理配置
        self.max_workers = 4
        self.timeout = 30
        
        self.logger.info("Enhanced Fund Trading System V3 initialized")
        self.logger.info("🔄 V3版本特性: 六大维度评估 + 智能冲突解决 + 动态权重管理")

        
    def analyze_fund_v3(self, fund_code: str) -> Dict[str, Any]:
        """V3版本的增强基金分析"""
        self.logger.info(f"Starting V3 enhanced analysis for fund {fund_code}")
        
        # 使用V3协调器进行分析
        analysis_result = self.coordinator.coordinate_analysis(fund_code)
        
        # 获取账户信息
        account_data = self.trading_agent.get_account_info()
        analysis_result['account_data'] = account_data
        
        return analysis_result
    
    def execute_trading_decision_v3(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """V3版本的交易决策执行"""
        fund_code = analysis_result.get('fund_code')
        enhanced_decision = analysis_result.get('enhanced_decision', {})
        
        # 获取决策信息
        final_decision = enhanced_decision.get('decision', 'hold')
        confidence = enhanced_decision.get('confidence', 0.0)
        
        if final_decision in ['buy', 'sell'] and confidence > 0.5:
            # 使用V3执行器进行交易
            execution_result = self.executor.execute_decision(analysis_result)
            
            # 如果V3执行器支持，同时使用真实交易代理
            if execution_result.get('execution_status') == 'executed':
                # 准备交易数据
                trade_data = {
                    'action': final_decision,
                    'fund_code': fund_code,
                    'price': execution_result.get('price', 1.0),
                    'quantity': execution_result.get('shares', '20000')
                }
                
                # 执行真实交易
                trade_result = self.trading_agent.process(trade_data)
                execution_result['real_trade_result'] = trade_result
                
                self.logger.info(f"Real trade executed for {fund_code}: {trade_result}")
            
            return execution_result
        else:
            return {
                'fund_code': fund_code,
                'action': 'hold',
                'reason': f"Decision: {final_decision}, Confidence: {confidence:.2f} (threshold: 0.5)",
                'timestamp': datetime.now().isoformat()
            }
    
    def run_trading_cycle_v3(self) -> List[Dict[str, Any]]:
        """运行V3版本的单次交易周期"""
        cycle_start_time = datetime.now()
        results = []
        
        # 测试基金列表
        fund_codes = self.buy_fund_list
        
        print(f"\n🔄 V3 交易周期开始 - {len(fund_codes)} 只基金 [{cycle_start_time.strftime('%H:%M:%S')}]")
        
        for fund_code in fund_codes:
            try:
                # 执行V3分析
                analysis_result = self.analyze_fund_v3(fund_code)
                
                if 'error' not in analysis_result:
                    # 展示详细分析结果
                    self.display_detailed_analysis_result(analysis_result)
                    
                    # 执行交易决策
                    execution_result = self.execute_trading_decision_v3(analysis_result)
                    result = {
                        'fund_code': fund_code,
                        'analysis_result': analysis_result,
                        'execution_result': execution_result
                    }
                else:
                    print(f"\n❌ 基金 {fund_code} 分析失败: {analysis_result['error']}")
                    result = {
                        'fund_code': fund_code,
                        'error': analysis_result['error']
                    }
                    
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Trading cycle error for {fund_code}: {e}")
                results.append({
                    'fund_code': fund_code,
                    'error': str(e)
                })
        
        # 周期结束摘要
        cycle_end_time = datetime.now()
        cycle_duration = (cycle_end_time - cycle_start_time).total_seconds()
        
        # 统计结果
        successful_analysis = len([r for r in results if 'error' not in r])
        failed_analysis = len([r for r in results if 'error' in r])
        
        # 统计决策类型
        decisions = {}
        for result in results:
            if 'error' not in result:
                analysis_result = result.get('analysis_result', {})
                enhanced_decision = analysis_result.get('enhanced_decision', {})
                decision = enhanced_decision.get('decision', 'hold')
                decisions[decision] = decisions.get(decision, 0) + 1
        
        # 简化输出
        decision_summary = " | ".join([f"{k.upper()}:{v}" for k, v in decisions.items()]) if decisions else "无决策"
        print(f"🏁 周期完成: 耗时{cycle_duration:.1f}s | 成功{successful_analysis}/{len(fund_codes)} | 决策分布: {decision_summary}\n")
        
        return results

    def display_detailed_analysis_result(self, analysis_result: Dict[str, Any]) -> None:
        """展示精简的分析结果"""
        try:
            fund_code = analysis_result.get('fund_code', 'UNKNOWN')
            enhanced_decision = analysis_result.get('enhanced_decision', {})

            # 核心决策结果 - 一行显示
            decision = enhanced_decision.get('decision', 'hold')
            confidence = enhanced_decision.get('confidence', 0)
            weighted_score = enhanced_decision.get('weighted_score', 0)

            decision_icon = "🟢" if decision == "buy" else ("🔴" if decision == "sell" else "🟡")
            print(f"\n{decision_icon} {fund_code}: {decision.upper()} | 置信度:{confidence:.2f} | 评分:{weighted_score:.3f}")

            # 显示市场分类
            market_classification = enhanced_decision.get('market_classification', {})
            classification = market_classification.get('primary_classification', '未知')
            print(f"   📊 市场分类: {classification}")

            # 显示维度评估摘要
            dimension_evaluations = enhanced_decision.get('dimension_evaluations', {})
            if dimension_evaluations:
                dim_summary = []
                for dim_name, dim_data in dimension_evaluations.items():
                    score = dim_data.get('score', 0)
                    state = dim_data.get('state', 'unknown')
                    dim_summary.append(f"{dim_name}:{score:.2f}")

                print(f"   🎯 维度评估: {' | '.join(dim_summary[:3])}")  # 只显示前3个

        except Exception as e:
            print(f"❌ {fund_code} 分析展示失败: {str(e)}")

    def run_trading_system_v3(self) -> None:
        """V3版本的自动化交易系统主循环"""
        self.logger.info("Starting V3 Enhanced Fund Trading System")

        try:
            # 主交易循环
            while ('09:27:00' <= datetime.now().strftime('%H:%M:%S') <= '11:27:00') or \
                  ('12:50:00' <= datetime.now().strftime('%H:%M:%S') <= '18:55:00'):

                # 运行V3交易周期
                results = self.run_trading_cycle_v3()

                # 休息间隔
                time.sleep(15)

        except KeyboardInterrupt:
            self.logger.info("V3 Trading system interrupted by user")
        except Exception as e:
            self.logger.error(f"V3 Trading system error: {e}")
        finally:
            self.logger.info("V3 Enhanced trading system stopped")

    def diagnose_system_status_v3(self, fund_code: str = '513500') -> Dict[str, Any]:
        """V3版本的系统诊断"""
        self.logger.info(f"🔧 V3系统诊断开始 - 基金: {fund_code}")

        diagnosis = {
            'fund_code': fund_code,
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'issues_found': [],
            'recommendations': [],
            'component_status': {},
            'v3_features_status': {}
        }

        try:
            # 1. 测试V3增强分析
            self.logger.info("🔍 测试V3六大维度分析...")
            analysis_result = self.analyze_fund_v3(fund_code)
            if 'error' in analysis_result:
                diagnosis['issues_found'].append("V3 enhanced analysis failed")
                diagnosis['component_status']['v3_analysis'] = 'failed'
            else:
                diagnosis['component_status']['v3_analysis'] = 'working'

            # 2. 测试传统代理状态
            self.logger.info("🔍 测试传统代理状态...")
            for agent_name, agent in [
                ('technical', self.technical_agent),
                ('enhanced_technical', self.enhanced_technical_agent),
                ('gua', self.gua_agent),
                ('flow', self.fund_flow_agent),
                ('soros', self.soros_agent)
            ]:
                try:
                    result = agent.process({'fund_code': fund_code})
                    if 'error' in result:
                        diagnosis['component_status'][agent_name] = 'error'
                        diagnosis['issues_found'].append(f"{agent_name} agent has errors")
                    else:
                        diagnosis['component_status'][agent_name] = 'working'
                except Exception as e:
                    diagnosis['component_status'][agent_name] = 'failed'
                    diagnosis['issues_found'].append(f"{agent_name} agent failed: {str(e)}")

            # 3. V3特性检查
            diagnosis['v3_features_status']['six_dimension_analysis'] = 'available'
            diagnosis['v3_features_status']['signal_conflict_resolution'] = 'available'
            diagnosis['v3_features_status']['dynamic_weight_management'] = 'available'
            diagnosis['v3_features_status']['fractal_validation'] = 'available'

            # 确定整体状态
            if len(diagnosis['issues_found']) == 0:
                diagnosis['overall_status'] = 'healthy'
            elif len(diagnosis['issues_found']) <= 2:
                diagnosis['overall_status'] = 'warning'
            else:
                diagnosis['overall_status'] = 'critical'

            self.logger.info(f"✅ V3系统诊断完成 - 状态: {diagnosis['overall_status']}")

        except Exception as e:
            diagnosis['overall_status'] = 'critical'
            diagnosis['issues_found'].append(f"System diagnosis failed: {str(e)}")
            self.logger.error(f"V3 system diagnosis error: {e}")

        return diagnosis
