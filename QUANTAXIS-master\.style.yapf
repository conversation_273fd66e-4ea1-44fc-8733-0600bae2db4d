[style]
based_on_style = google
spaces_before_comment = 2, 4
SPLIT_ARGUMENTS_WHEN_COMMA_TERMINATED = true
SPLIT_ALL_COMMA_SEPARATED_VALUES = true
SPLIT_BEFORE_BITWISE_OPERATOR = true
SPLIT_BEFORE_CLOSING_BRACKET = true
SPLIT_BEFORE_DICT_SET_GENERATOR = true
SPLIT_BEFORE_DOT = true
SPLIT_BEFORE_EXPRESSION_AFTER_OPENING_PAREN = true
SPLIT_BEFORE_FIRST_ARGUMENT = true
SPLIT_BEFORE_LOGICAL_OPERATOR = true
SPLIT_BEFORE_NAMED_ASSIGNS = true
SPLIT_COMPLEX_COMPREHENSION = true
DEDENT_CLOSING_BRACKETS = true
