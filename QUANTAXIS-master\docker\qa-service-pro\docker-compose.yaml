version: "2.1"
services:

    redis:
        image: 'daocloud.io/quantaxis/qaredis:latest'
        ports:
            - "6379:6379"
        environment:
            - TZ=Asia/Shanghai
        command: ['redis-server']
        restart: always
    postgres:
        image: postgres:11
        environment:
            - POSTGRES_USER=airflow
            - POSTGRES_PASSWORD=airflow
            - POSTGRES_DB=airflow
        volumes:
            - pg-data:/var/lib/postgresql/data
        ports:
            - "5432:5432"
        restart: always
    mgdb:
        image: daocloud.io/quantaxis/qamongo_single:latest
        ports:
            - "27017:27017"
        environment:
            - TZ=Asia/Shanghai
            - MONGO_INITDB_DATABASE=quantaxis
        volumes:
            - qamg:/data/db
        networks:
            qanetwork_pro:
                ipv4_address: **********
        restart: always

    qaeventmq:
        image: daocloud.io/quantaxis/qaeventmq:latest
        ports:
            - "15672:15672"
            - "5672:5672"
            - "4369:4369"
        environment:
            - TZ=Asia/Shanghai
        networks:
            qanetwork_pro:
                ipv4_address: **********
        restart: always

    qa:
        image: daocloud.io/quantaxis/qacommunity-rust-go:allin-20210218
        container_name: qacommunity-rust
        depends_on:
            - mgdb
            - qaeventmq
        networks:
            qanetwork_pro:
                ipv4_address: **********
        ports:
            - "8888:8888"
            - "81:80"
            - "8787:8787"
        environment:
          - TZ=Asia/Shanghai
          - LANG=C.UTF-8
          - MONGODB=mgdb
          - QARUN=qaweb
          - QAPUBSUB_IP=qaeventmq
          - QAPUBSUB_PORT=5672
          - QAPUBSUB_USER=admin
          - QAPUBSUB_PWD=admin
        volumes:
            - qacode:/root/code
            - qadag:/dag
            - qaconf:/root/otg/conf
            - qauser:/root/otg/user
            - qalog:/root/otg/log
        restart: always


    qaweb:
        image: daocloud.io/quantaxis/qacommunity-rust-go:allin-20210218
        container_name: qaweb
        depends_on:
            - mgdb
            - qaeventmq
        networks:
            qanetwork_pro:
                ipv4_address: **********
        ports:
            - "8010:8010"
            - "8018:8018"
            - "8019:8019"
            - "8028:8028"
            - "8029:8029"
        environment:
            - MONGODB=mgdb
            - QAPUBSUB_IP=qaeventmq
            - QAPUBSUB_PORT=5672
            - QAPUBSUB_USER=admin
            - QAPUBSUB_PWD=admin
            - QARUN_AMQP=pyamqp://admin:admin@qaeventmq:5672//
            - TZ=Asia/Shanghai
        restart: always
        command: ['/root/wait_for_it.sh', 'qaeventmq:5672', '--' , "/root/runcelery.sh"]


    open-trade-gateway:
        image: daocloud.io/quantaxis/opentradegateway:latest

        ports:
          - "7788:7788"
        command: ['open-trade-gateway']
          
        volumes:
            - qaconf:/etc/open-trade-gateway
            - qauser:/var/local/lib/open-trade-gateway
            - qalog:/var/log/open-trade-gateway
        networks:
            qanetwork_pro:
                ipv4_address: ***********

        restart: always

        
    qamonitor:
        image: daocloud.io/quantaxis/qa-monitor:latest
        ports:
            - "61209:61209"
            - "61208:61208"
        pid: "host"
        networks:
            qanetwork_pro:
                ipv4_address: **********

        restart: always

    qactpbeebroker:
        image: daocloud.io/quantaxis/qactpbeebroker:latest
        ports:
            - "5000:5000"
        networks:
            qanetwork_pro:
                ipv4_address: **********

            # --userid TEXT
            # --password TEXT
            # --brokerid TEXT
            # --mdaddr TEXT
            # --tdaddr TEXT
            # --appid TEXT
            # --authcode TEXT

        command: ['/root/wait_for_it.sh', 'qaeventmq:15672', '--' , "QACTPBEE", "--userid", "133496", "--password", 'QCHL1234']

    qamarketcollector:
        image: daocloud.io/quantaxis/qarealtimecollector:latest
        ports:
            - "8011:8011"
        depends_on:
            - mgdb
            - qaeventmq
        environment:
            - MONGODB=mgdb
            - EventMQ_IP=qaeventmq
        networks:
            qanetwork_pro:
                ipv4_address: **********
        command:
            ['/root/QUANTAXIS_RealtimeCollector/docker/wait_for_it.sh', 'qaeventmq:5672', '--' ,'/root/QUANTAXIS_RealtimeCollector/docker/start_collector.sh']


    qaclickhouse:
        image: daocloud.io/quantaxis/qa-clickhouse
        ports:
            - "9000:9000"
            - "8123:8123"
            - "9009:9009"
        environment:
            - TZ=Asia/Shanghai
        networks:
            qanetwork_pro:
                ipv4_address: ***********

    qatrader:
        image: daocloud.io/quantaxis/qatrader:latest
        ports:
            - "8020:8020"
        depends_on:
            - mgdb
            - qaeventmq
        environment:
            - MONGODB=mgdb
            - QAPUBSUB_IP=qaeventmq
            - QAPUBSUB_PORT=5672
            - QAPUBSUB_USER=admin
            - QAPUBSUB_PWD=admin
            - QARUN_AMQP=pyamqp://admin:admin@qaeventmq:5672//
            - TZ=Asia/Shanghai
        command:
            ['/root/QATrader/docker/wait_for_it.sh', 'qaeventmq:5672', '--' ,'qatraderserver']

        networks:
            qanetwork_pro:
                ipv4_address: ***********


    webserver:
        image: puckel/docker-airflow
        restart: always
        depends_on:
            - postgres
            - redis
        environment:
            - LOAD_EX=n
            - FERNET_KEY=46BKJoQYlPPOexq0OhDZnIlNepKFf87WFwLbfzqDDho=
            - EXECUTOR=Celery
            # - POSTGRES_USER=airflow
            # - POSTGRES_PASSWORD=airflow
            # - POSTGRES_DB=airflow
            # - REDIS_PASSWORD=redispass
        volumes:
            - qadag:/usr/local/airflow/dags
            # Uncomment to include custom plugins
            # - ./plugins:/usr/local/airflow/plugins
        ports:
            - "8080:8080"
        command: webserver
        healthcheck:
            test: ["CMD-SHELL", "[ -f /usr/local/airflow/airflow-webserver.pid ]"]
            interval: 30s
            timeout: 30s
            retries: 3

    flower:
        image: puckel/docker-airflow
        restart: always
        depends_on:
            - redis
        environment:
            - EXECUTOR=Celery
            # - REDIS_PASSWORD=redispass
        ports:
            - "5555:5555"
        command: flower

    scheduler:
        image: puckel/docker-airflow
        restart: always
        depends_on:
            - webserver
        volumes:
            - qadag:/usr/local/airflow/dags
            # Uncomment to include custom plugins
            # - ./plugins:/usr/local/airflow/plugins
        environment:
            - LOAD_EX=n
            - FERNET_KEY=46BKJoQYlPPOexq0OhDZnIlNepKFf87WFwLbfzqDDho=
            - EXECUTOR=Celery
            # - POSTGRES_USER=airflow
            # - POSTGRES_PASSWORD=airflow
            # - POSTGRES_DB=airflow
            # - REDIS_PASSWORD=redispass
        command: scheduler

    worker:
        image: puckel/docker-airflow
        restart: always
        depends_on:
            - scheduler
        volumes:
            - qadag:/usr/local/airflow/dags
            # Uncomment to include custom plugins
            # - ./plugins:/usr/local/airflow/plugins
        environment:
            - FERNET_KEY=46BKJoQYlPPOexq0OhDZnIlNepKFf87WFwLbfzqDDho=
            - EXECUTOR=Celery
            # - POSTGRES_USER=airflow
            # - POSTGRES_PASSWORD=airflow
            # - POSTGRES_DB=airflow
            # - REDIS_PASSWORD=redispass
        command: worker


    editer:
        image: daocloud.io/quantaxis/qaeditor:latest
        container_name: qaeditor
        networks: 
            qanetwork_pro:
                ipv4_address: **********4
        ports: 
            - "3001:3000"
        environment:
            - TZ=Asia/Shanghai
            - LANG=C.UTF-8
            - MONGODB=mgdb
            - QARUN=qaweb
            - QAPUBSUB_IP=qaeventmq
            - QAPUBSUB_PORT=5672
            - QAPUBSUB_USER=admin
            - QAPUBSUB_PWD=admin
        restart: always
        volumes: 
            - qacode:/home/<USER>
volumes:
    qamg:
        external:
            name: qamg
    qacode:
        external:
            name: qacode
    pg-data:
        external:
            name: pg-data
    qadag:
        external:
            name: qadag
    qaconf:
        external:
            name: qaconf
    qauser:
        external:
            name: qauser
    qalog:
        external:
            name: qalog
networks:
    qanetwork_pro:
        ipam:
            config:
                - subnet: **********/24
                  gateway: **********
