apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: E:\k8s-for-docker-desktop\kompose-windows-amd64.exe convert --replication-controller
      --replicas 3
    kompose.version: 1.18.0 (06a2e56)
  creationTimestamp: null
  labels:
    io.kompose.service: qa
  name: qa
spec:
  ports:
  - name: "8888"
    port: 8888
    targetPort: 8888
  - name: "81"
    port: 81
    targetPort: 80
  selector:
    io.kompose.service: qa
status:
  loadBalancer: {}
