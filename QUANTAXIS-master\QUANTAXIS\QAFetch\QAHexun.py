
# shibor  
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 隔夜
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 一周
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 两周
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 三周
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 一个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 两个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 三个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 四个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 六个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 七个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 八个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 九个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 十个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 十一个月
#http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r=****************&t=31&ts=************* # 十二个月

import requests
import time
import pandas as pd
from QUANTAXIS.QAFetch.base import headers
from copy import deepcopy
headers_hexun = deepcopy(headers)
headers_hexun['Referer'] = 'http://data.bank.hexun.com/'
headers_hexun['Host'] = 'data.bank.hexun.com'
headers_hexun['X-Requested-With'] = 'XMLHttpRequest'


chibor_url = 'http://data.bank.hexun.com/dataprovider/BankOfferedrateFlash.ashx?r={}&t=31&ts={}'

def QA_fetch_get_chibor(frequence='1D'):
    if frequence == '1D':
        d = '****************'
    elif frequence == '1W':
        d = '****************'
    elif frequence == '2W':
        d = '****************'
    elif frequence == '3W':
        d = '****************'
    elif frequence == '1M':
        d = '****************'
    elif frequence == '2M':
        d = '****************'
    elif frequence == '3M':
        d = '****************'
    elif frequence == '4M':
        d = '****************'
    elif frequence == '5M':
        d = '****************'
    elif frequence == '6M':
        d = '****************'
    elif frequence == '7M':
        d = '****************'
    elif frequence == '8M':
        d = '****************'
    elif frequence == '9M':
        d = '****************'
    elif frequence == '10M':
        d = '****************'
    elif frequence == '11M':
        d = '****************'
    elif frequence == '12M':
        d = '****************'

    res = requests.get(chibor_url.format(d, int(time.time()*1000)-1), headers= headers_hexun).text
    data = [{'date':d[12:22],'1D':float(d[31:35])} for d in res.split('\r\n') if d[1:5] == 'date']
    return pd.DataFrame(data).set_index('date')