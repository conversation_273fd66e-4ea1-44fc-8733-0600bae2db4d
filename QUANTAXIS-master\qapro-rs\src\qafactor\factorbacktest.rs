use actix_web::{middleware, web, App, HttpRequest, HttpResponse, HttpServer};

use crate::qaenv::localenv::CONFIG;
use uuid::Uuid;

use crate::qaaccount::account::QA_Account;
use crate::qaprotocol::mifi::qafastkline::QAKlineBase;
use crate::qaprotocol::qifi::{
    account::Trade, account::QIFI, func::from_serde_value, func::from_string,
};
use std::collections::{BTreeMap, HashMap};
extern crate serde;
extern crate serde_json;
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Serialize, Clone, Deserialize, Debug)]
pub struct model {
    pub start: String,
    pub end: String,
    pub init_cash: f64,
    pub codelist: Vec<String>,
    pub weights: HashMap<String, HashMap<String, f64>>,
}

struct DailyOrderSchedule {
    code: String,
    amount: f64,
    price: f64,
    datetime: String,
}
impl model {
    pub fn default() -> Self {
        let w:HashMap<String, HashMap<String, f64>> = serde_json::from_str(r#"{"2018-08-22":{"000001":0.1,
        "000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,
        "000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,
        "000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,
        "000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,
        "000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,
        "000055":0.0,"000056":0.0,"000058":0.0,"000059":0.1,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,
        "000065":0.0},"2018-08-23":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,
        "000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,
        "000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,
        "000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,
        "000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,
        "000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,
        "000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-08-24":{"000001":0.0,"000002":0.0,"000004":0.0,
        "000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-08-27":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-08-28":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-08-29":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-08-30":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-08-31":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-03":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-04":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-05":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-06":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-07":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-10":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-11":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-12":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-13":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-14":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-17":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0},"2018-09-18":{"000001":0.0,"000002":0.0,"000004":0.0,"000005":0.0,"000006":0.0,"000007":0.0,"000008":0.0,"000009":0.0,"000010":0.0,"000011":0.0,"000012":0.0,"000014":0.0,"000016":0.0,"000017":0.0,"000018":0.0,"000019":0.0,"000020":0.0,"000021":0.0,"000023":0.0,"000025":0.0,"000026":0.0,"000027":0.0,"000028":0.0,"000029":0.0,"000030":0.0,"000031":0.0,"000032":0.0,"000034":0.0,"000035":0.0,"000036":0.0,"000037":0.0,"000038":0.0,"000039":0.0,"000040":0.0,"000042":0.0,"000043":0.0,"000045":0.0,"000046":0.0,"000048":0.0,"000049":0.0,"000050":0.0,"000055":0.0,"000056":0.0,"000058":0.0,"000059":0.0,"000060":0.0,"000061":0.0,"000062":0.0,"000063":0.0,"000065":0.0}}"#).unwrap();
        println!("{:#?}", w);
        let code = [
            "000001", "000002", "000004", "000005", "000006", "000007", "000008", "000009",
            "000010", "000011", "000012", "000014", "000016", "000017", "000018", "000019",
            "000020", "000021", "000023", "000025", "000026", "000027", "000028", "000029",
            "000030", "000031", "000032", "000034", "000035", "000036", "000037", "000038",
            "000039", "000040", "000042", "000043", "000045", "000046", "000048", "000049",
            "000050", "000055", "000056", "000058", "000059", "000060", "000061", "000062",
            "000063", "000065",
        ];
        let codelist: Vec<String> = code.iter().map(|x| x.to_string()).collect();
        model {
            start: "2018-08-22".to_string(),
            end: "2018-09-20".to_string(),
            init_cash: 1000000.0,
            codelist,
            weights: w,
        }
    }
    pub fn get_weight(&mut self, date: String, code: String) -> f64 {
        let dtr = self.weights.get_mut(date.as_str());
        if dtr.is_some() {
            dtr.unwrap().get(code.as_str()).unwrap_or(&0.0).clone()
        } else {
            0.0
        }
    }
}

pub struct QAModelWeights {
    // 开始结束时间,
    data: Vec<QAKlineBase>,
    pub model: model,
    date: String,
    last_weight: HashMap<String, f64>,
    account: QA_Account,
    res: Vec<QIFI>,
}

impl QAModelWeights {
    fn generate(model: model) -> Self {
        // 生成随机
        let init_cash = model.init_cash.clone();
        let acc = Uuid::new_v4().to_string();
        QAModelWeights {
            data: vec![],
            model: model,
            date: "".to_string(),

            last_weight: HashMap::new(),
            account: QA_Account::new(acc.as_str(), "factor", "admin", init_cash, false, "real"),
            res: vec![],
        }
    }

    fn get_data_from_csv(&mut self) -> Vec<QAKlineBase> {
        let filepath = "E:\\QARS\\qafactor-rs\\dataset.csv".to_string();
        let mut u = vec![];
        let mut rdr = csv::Reader::from_path(&filepath).unwrap();
        for result in rdr.deserialize() {
            let bar: QAKlineBase = result.unwrap();
            u.push(bar);
        }
        u
    }

    fn next_day(&mut self, date: String) {
        // 用于处理下一天的函数(settle)
        let commission = self.account.accounts.commission;
        //println!("{:#?} ", commission);
        self.res.push(self.account.get_qifi_slice());
        self.account.settle();
        self.date = date;
    }
    fn get_lastweight(&mut self, code: &str) -> f64 {
        (self.last_weight.get(code).unwrap_or(&0.0)).clone()
    }

    fn save_qifires(&mut self, slice: QIFI) {
        let trading_day = slice.trading_day.clone();
    }

    fn apply_weights(&mut self) {
        /// apply_weights:
        ///
        /// 目前 use backtest model
        ///
        /// 1. get_data => Vec<QAKlineBase>
        /// 2. get_daypanel =>  init daily panel
        ///     ///账户 receive 最新市值
        ///     /// 调仓
        /// 3. day_switch
        ///     账户 base on weights => 生成调仓订单
        ///
        ///     优先卖出订单 /  再进行买入部分
        ///
        /// 4. day_settle
        ///
        /// backtestmodel should add qifi support
        ///
        let mut data = self.get_data_from_csv();

        let mut day_panel: Vec<&QAKlineBase> = vec![]; // data vessl for st
        let mut orderSchedule: Vec<DailyOrderSchedule> = vec![];
        for ir in data.iter_mut() {
            if self.date != ir.datetime {
                let balance = self.account.get_balance();
                // let money = self.account.money;
                // let fp = self.account.get_floatprofit();
                // let commission = self.account.accounts.commission;
                // println!(
                //     "dynamic balance: {:#?} money {:#?} floatprofit {:#?} commission {:#?}",
                //     balance, money, fp, commission
                // );

                for i in day_panel {
                    //println!("{:#?}", i);
                    let w = self
                        .model
                        .get_weight(i.datetime.to_string(), i.code.to_string());

                    let new_mv = w * balance;

                    let pos = self.account.get_position(i.code.as_str()).unwrap();
                    let old_mv = pos.open_cost_long + pos.float_profit();
                    let mv_diff = new_mv - old_mv;
                    //println!("{} - {} - {} - {}", i.date, i.code, i.open, w);
                    //let weight_diff: f64 = w - self.get_lastweight(i.code.as_str());
                    //println!("wdiff{:#?} - w {:#?}", weight_diff, w, );
                    // weight_diff -> weight new - weight old
                    // weight_diff>0  ++ buy /  <0  -- sell
                    if mv_diff != 0.0 {
                        //self.last_weight.insert(i.code.clone(), w);
                        if mv_diff > 0.0 {
                            //println!("buy {}-{}-{}-{}",weight_diff,((100*((weight_diff*init_cash/(i.open*100.0)) as i32)) as f64).abs(),i.date.as_str(), i.open.clone() );

                            // buy order should be sent in last
                            orderSchedule.push(DailyOrderSchedule {
                                code: i.code.clone(),
                                amount: ((100 * ((mv_diff / (i.open * 100.0)) as i32 - 1)) as f64)
                                    .abs(),
                                price: i.open,
                                datetime: i.datetime.clone(),
                            })
                        } else if mv_diff < 0.0 {
                            //println!("sell {}-{}-{}-{}",weight_diff,((100*((weight_diff*init_cash/(i.open*100.0)) as i32)) as f64).abs(),i.date.as_str(), i.open.clone() );

                            let mut vol =
                                ((100 * ((mv_diff / (i.open * 100.0)) as i32 - 1)) as f64).abs();

                            if vol > self.account.get_volume_long(i.code.as_str()) {
                                println!("raw volume diff {:#?}", vol);
                                vol = self.account.get_volume_long(i.code.as_str());
                                println!("adjust to {:#?}", vol);
                            }
                            if w == 0.0 {
                                vol = self.account.get_volume_long(i.code.as_str());
                                //println!("not weight in current {:#?}", vol);
                            }
                            self.account
                                .sell(i.code.as_str(), vol, i.datetime.as_str(), i.open);
                        }
                    }
                }
                for order in orderSchedule.iter() {
                    self.account.buy(
                        order.code.as_str(),
                        order.amount,
                        order.datetime.as_str(),
                        order.price,
                    );
                }

                // reinit in day switch
                orderSchedule = vec![];
                day_panel = vec![];
                self.next_day(ir.datetime.clone());
            }
            // println!(
            //     "MARKET DATA {:#?}, {:#?}, {:#?}",
            //     ir.code.clone(),
            //     ir.open.clone(),
            //     ir.date.clone()
            // );
            self.account
                .on_price_change(ir.code.clone(), ir.open.clone(), ir.datetime.clone());
            day_panel.push(ir);
        }
    }
}
/// weights:
/// { date: {
///     code: weight}
/// }
/// {"2020-01-03":{
///     "000001": 0.2,
///     "000004": 0.5,
///     "600010": 0.2
///     "000008": 0.1}
///  "2020-01-03":{
///     "000001": 0.2,
///     "000004": 0.5,
///     "600010": 0.2
///     "000008": 0.1}
/// }
pub async fn submit_weights(item: web::Json<model>, srv: web::Data<()>) -> HttpResponse {
    //println!("{:#?}", item.codelist);
    //println!("{:#?}", item.codelist);
    let mut mw = QAModelWeights::generate(model {
        start: item.start.clone(),
        end: item.end.clone(),
        init_cash: item.init_cash.clone(),
        codelist: item.codelist.clone(),
        weights: item.weights.clone(),
    });
    for code in item.codelist.clone().iter() {
        mw.account.init_h(code);
    }
    mw.get_data_from_csv();
    mw.apply_weights();
    println!("finish backtest");
    let res = mw.res.clone();
    let mut tes = vec![];

    for slice in res.iter() {
        if slice.trading_day <= mw.model.end && slice.trading_day >= mw.model.start {
            //mw.save_qifires(slice.clone());
            tes.push(slice.clone())
        }
    }
    println!("prepare to save!!");

    HttpResponse::Ok().json(mw.account.account_cookie)
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_new() {
        let mut mw = QAModelWeights::generate(model::default());
        mw.apply_weights();
        println!("{:#?}", mw.account.trades);
        //println!()
    }
}
