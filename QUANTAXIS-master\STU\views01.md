量化做了有快2年多了, 逐步对于市场有了一定的了解, 现在对此进行梳理


## 量化是什么, 量化要做什么


### 有毒的苹果

很多时候, 大家对于量化的理解在于模型是不是赚钱, 这个问题很常见也很好, 但是其实是一个有毒的苹果. 为什么说一个明星策略是一个有毒的苹果呢, 这个事情我们要从普遍的社会学角度去相互理解. 从社会学角度上, 一个既有决策能力的人 又有行动能力的人是非常稀缺并且难以出现的, 也就是换个场景说, 90%的人都没法成为一个将军, 没法成为一个马云, 成为一个乔布斯. 这是非常正常的现象, 并且 这也不代表你的行为的结果是错误的. 因此, 90%的人 或者说  市面上90%的策略是赚不到大钱的这个事情, 才是本质上正确的事情.

而事实上, 很多非从业人员, 或者一些从业人员, 在量化的时候, 走到这一步, 基本上就准备放弃了. 原因也很简单, 就是机会成本过高. 当你不来做策略的时候, 你可以通过其他的事情来产生价值(实在不行出卖体力来搬砖, 所谓的无风险收益). 尤其是目前市面上, 想从一个想法 做成一个完整能用的策略, 换个角度理解, 想怀胎十月生出一个功能健全的baby, 本身就消磨掉很大的耐心和希望了. 但是事实上是, 这只是个开始, 而不是你策略的结束

所以有毒的苹果的意思就是, 

苹果看起来是好东西 ==> 一个很牛逼的策略(IC高上天际) 非常赚钱, 年化上百上千, 夏普率吊打一切 没问题

换个角度想, 你03年投资阿里巴巴 99年入股苹果, 你的收益就是这样的


但是苹果是有毒的 ==> 你的期望值变得非常高, 你希望一出生就是500强CEO, 你对每个策略的判断就是  赚钱就是好的 亏钱就是差的, 不赚不亏也是垃圾

最后在挣扎了一段时间后, 选择放弃, 回归对于你而言的边际收益最大的事情

### 90%的普通人的意义在哪里

从上面我们也大概能理解了, 从社会学的角度去看, 90%的人或者事情, 注定在一个体系里面会沦为平庸, 但是你需要想的事情是,  这样的人就是没有意义的吗

每个小朋友心里都做过上北大清华的梦, 90%的人都考不上, 这群人就没有意义了吗. 你做了10个策略9个策略都是亏亏赚赚, 这9个策略就是没有意义的吗, 再推广一下, 每个军队里面都有90%的人是基层士兵, 这些士兵就是没有意义的吗

你心里清楚他们是存在意义的, 但是核心是你没有一套组织体系, 这个组织体系, 才是让这90%的人来实现意义的方式

### 从单项式模型说起

Y = λ1X1

我们先看一个单项式模型, 这就是典型的一元一次函数, 他实现的事情是什么

首先  你的点越规律, 这个单项式拟合的结果就越好 ==> 换句话说, 在一个非常简单的金融市场,  单项式模型是生效的, 这就是为什么当市场出现牛市, 或者在市场形成初期, 一些你看起来没有什么含量的策略也能赚到钱的原因

其次, 单向式模型的lambda 你可以理解为你的跟单效率, 也可以理解为上面我们说的组织结构的效率

在一个简单的市场/牛市中, X起着决定性的作用, 而lambda意义不大, 只要你没有完全坐反掉, lambda = -1, 你基本上都能赚钱, 从社会学的角度去思考,  只要大佬(X) 让你干A这个事情, 你没有完全反着干, 你就能成功

### 说到多项式

你会发现, 问题来了, 前面讲了这么多, 并没有讲到量化赚钱的本质, 似乎扯了一堆乱七八糟的社会学鸡汤.

这里就是关键

```当市场趋于复杂, 当行情逐步让人难以理解和琢磨的时候, 你需要研究的 不应该是X, 而应该是lambda```

Y = λ1X1 + λ2X2 + λ3X3 + λnXn

这里的X 里面包含了我前面说的毒苹果, 也包含了90%的普通人, 既有牛逼的回测结果的策略, 也有垃圾策略, 既有震荡策略 也有趋势策略

我们做量化需要研究的, 不是把单个Xi做到极致, 而是通过一个组织的体系, 把 λ做到极致

这里我们先搁置, 我们先来看一个分类器的故事

在最初, 简单的线性回归就可以把一个事情分为两类, 但是当事情变得复杂起来以后, 简单的单项式并不能解决问题

![image.png](http://picx.gulizhu.com/FtkKBu8Jw8qmgcundfVOoU9a61wK)
我拿ppt来画一个图, 这时候, 你应该怎么去划分这个事情

有的人依然在X上做文章, 这就出现了SVM

SVM通过核函数映射, 把他映射到高维空间, 就可以继续进行线性分类了

![image.png](http://picx.gulizhu.com/FlezFqjH8Khu0lIt8J-73IcyayIb)

这时候, 你依然可以通过一个trick+ 线性分类来解决问题


但是当事情变得越来越复杂, 特征越来越多的时候, 你就需要神经网络模型

![image.png](http://picx.gulizhu.com/Fr_WQ0A0LYqcw3FQoD7myWiBXojD)

神经网络的本质是什么?  神经网络的本质就是一个多项式模型, 他不是管理x的 是管理λ的, 通过简单的BP反向传播, 我们就可以实现一个权重的调整


在这里 我们并不对于机器学习进行特别多的展开, 我们核心在讨论金融和量化

但是你已经可以基本初步理解了, 一套行之有效的组织体系, 才是化腐朽为神奇的关键之处

及 ==> 每个个体都不足够强壮的军队, 形成一个合力以后就会所向披靡

这个合力的形成, 依赖的是对于λ的管理, 在现实中 是依赖  班长-排长-营长-团长 这个管理体系以及不同班的规模, 团的规模, 来构建这个λ体系的

### 说回到量化

说了这么多, 我们再讲回量化, 我们需要的是一个策略就能打天下赚几个亿的事情吗, 并非如此, 也非常难以做到(绝不是说没有)


量化的目的 是不断的产生Xn  以及 有效的管理λn 来实现这个Y的拟合程度的


换言之 用传统金融理论来套回去 

![image.png](http://picx.gulizhu.com/Fv0ATfHAlNmOWvoR8i7EZMEzgbaQ)

在目前的市场, 靠一个IC超高的策略打天下, 应该是交易员/手工的事情, 做量化的人, 天生就不要指望你的垃圾代码能实现一个超过人脑的IC策略

这个是很多新手的误区



由此我们得出一些结论:


1. 一个垃圾策略的产生是必然且常见的
2. 策略写出来 是你的体系的开始 而不是结束
3. 关注多项式模型, 这是从社会学角度管理的基础的模型
4. 有效的管理体系, 有效的权重跟踪才是让90%的人发挥价值的地方
5. 你应该生产更多减少共线性的策略, 及如果你做了100个策略 但都是一个类型的重复, 他和做一个策略没有任何区别
6. 单策略打天下这个事情, 有可能赚钱, 仅在行情单一的情况下, 但是你也可以理解成 这个行情谁都能赚钱(比如2014年的股市)
7. 量化靠的是BR赚钱 切记切记



量化应该专注在BR上, 多去思考一下BR, 如何实现行之有效的管理体系, 来通过λ管理你的模型的信号




### 收尾


到这里, 我基本对于当前量化怎么赚钱, 赚谁的钱, 怎么构建赚钱的体系有个偏理论性的描述了, QUANTAXIS目前就是在这个方向上往前进行

基本上对于我而言, 一个策略的产生 大概只需要4个小时,  我就可以把它进行分析和标准化, 进入到我的策略工厂体系中 ==> 变成一个Xn

然后我现在正在构建, 或者基本上已经投入实盘测试的, 就是这个标准化的Xn流程 和 lambda管理体系的综合表现

![image.png](http://picx.gulizhu.com/FsMe1TiNoP3YH0wrtBdPb6GPx2kE)
最后再把这张好早以前的图放回来, 我们基本已经基于这个体系构建了 SOP和KPI, 目前就是即将见到第一条流水线的状态