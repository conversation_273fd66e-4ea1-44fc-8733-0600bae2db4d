"""
技术分析智能体
负责基金的技术分析，包括趋势判断、买卖信号生成等
"""

from typing import Dict, Any
from datetime import datetime
from ..base_agent import BaseAgent
from ...core.utils import *


class TechnicalAgent(BaseAgent):
    """
    @class TechnicalAgent
    @brief 技术分析智能体
    @details 负责基金的技术分析，包括趋势判断、买卖信号生成等
    """
    
    def __init__(self, name: str = "TechnicalAgent"):
        super().__init__(name, "technical_analysis")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 处理技术分析请求
        @param data: 包含基金代码的数据
        @return: 技术分析结果
        """
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
            
        try:
            analysis_result = self.analyze_fund_trend(fund_code)
            return analysis_result
        except Exception as e:
            self.logger.error(f"Technical analysis failed for {fund_code}: {str(e)}")
            return {'error': str(e)}
    
    def analyze_fund_trend(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 分析基金趋势 - 完全基于真实数据
        @param fund_code: 基金代码
        @return: 分析结果
        """
        try:
            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")
                
            # 获取增强版技术指标
            tech_data = get_enhanced_technical_indicators(fund_code, 'D')
            indicators = tech_data['indicators']
            
            # 数据质量验证
            quality_result = validate_data_quality(fund_code, tech_data)
            if not quality_result['overall_quality']:
                raise ValueError(f"Data quality issues: {quality_result['issues']}")
            
            # 计算置信度
            confidence_score = get_real_confidence_metrics(fund_code, indicators)
            
            # 生成买卖信号 - 基于多个真实指标确认
            signal_conditions = {
                'ma_trend': indicators['ma5'] > indicators['ma20'],
                'rsi_condition': 30 < indicators['rsi'] < 70,  # RSI不在极值区域
                'macd_bullish': indicators['macd_bullish'],
                'volume_confirmation': indicators['volume_ratio'] > 1.2,
                'bb_position': 0.2 < indicators['bb_position'] < 0.8  # 布林带中性区域
            }
            
            # 计算信号强度
            positive_signals = sum([
                signal_conditions['ma_trend'],
                signal_conditions['macd_bullish'],
                signal_conditions['volume_confirmation']
            ])
            
            # 风险控制信号
            risk_signals = sum([
                not signal_conditions['rsi_condition'],  # RSI极值
                not signal_conditions['bb_position']     # 布林带极值
            ])
            
            # 综合判断
            if positive_signals >= 2 and risk_signals == 0:
                buy_signal = True
                if positive_signals == 3 and indicators['volume_ratio'] > 2.0:
                    signal_strength = 'strong'
                else:
                    signal_strength = 'medium'
            elif positive_signals >= 1 and risk_signals <= 1:
                buy_signal = True
                signal_strength = 'weak'
            else:
                buy_signal = False
                signal_strength = 'none'
            
            # 趋势强度分析
            trend_data = calculate_trend_strength_metrics(fund_code)
            
            return {
                'fund_code': fund_code,
                'buy_signal': buy_signal,
                'signal_strength': signal_strength,
                'confidence_score': confidence_score,
                'technical_indicators': {
                    'ma5': indicators['ma5'],
                    'ma20': indicators['ma20'],
                    'ma60': indicators['ma60'],
                    'rsi': indicators['rsi'],
                    'macd': indicators['macd'],
                    'macd_signal': indicators['macd_signal'],
                    'bb_position': indicators['bb_position'],
                    'volume_ratio': indicators['volume_ratio'],
                    'atr_ratio': indicators['atr_ratio']
                },
                'trend_analysis': trend_data,
                'signal_details': signal_conditions,
                'analysis_time': datetime.now().isoformat(),
                'data_quality': quality_result['quality_score'],
                'data_source': 'real_quantaxis'
            }

        except Exception as e:
            self.logger.error(f"Failed to analyze fund trend for {fund_code}: {str(e)}")
            # 不再使用模拟数据，直接返回错误
            return {
                'fund_code': fund_code,
                'error': str(e),
                'buy_signal': False,
                'signal_strength': 'none',
                'confidence_score': 0.0,
                'data_source': 'error'
            }
