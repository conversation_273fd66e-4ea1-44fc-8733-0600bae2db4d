# WINDOWS开启自动脚本

<!-- TOC -->

- [WINDOWS开启自动脚本](#windows开启自动脚本)
    - [打开控制面板-系统和安全-管理工具](#打开控制面板-系统和安全-管理工具)
    - [打开计划任务程序](#打开计划任务程序)
    - [在计划任务程序中,新建任务](#在计划任务程序中新建任务)
    - [创建QUANTAXIS_Update任务](#创建quantaxis_update任务)
    - [选择运行时间/频率](#选择运行时间频率)
    - [选择执行的命令](#选择执行的命令)
    - [配置完毕](#配置完毕)

<!-- /TOC -->

我们使用计划任务来开启自动更新任务:


## 打开控制面板-系统和安全-管理工具
![](http://picx.gulizhu.com/management.png)

## 打开计划任务程序
![](http://picx.gulizhu.com/management2.png)

## 在计划任务程序中,新建任务
![](http://picx.gulizhu.com/task1.png)

## 创建QUANTAXIS_Update任务
![](http://picx.gulizhu.com/task2.png)

## 选择运行时间/频率
![](http://picx.gulizhu.com/task3.png)

## 选择执行的命令
![](http://picx.gulizhu.com/task4.png)

## 配置完毕
![](http://picx.gulizhu.com/task5.png)