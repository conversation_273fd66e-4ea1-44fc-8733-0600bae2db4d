version: "3.7"
services:

    redis:
        image: 'daocloud.io/quantaxis/qaredis:latest'
        ports:
            - "6379:6379"
        env_file: env.env
        command: ['redis-server']
        restart: always
    postgres:
        image: postgres:11
        env_file: env.env
        volumes:
            - pg-data:/var/lib/postgresql/data
        ports:
            - "5432:5432"
        restart: always
    mgdb:
        image: daocloud.io/quantaxis/qamongo_single:latest
        ports:
            - "27017:27017"
        env_file: env.env
        volumes:
            - qamg:/data/db
        networks:
            qanetwork_pro:
                ipv4_address: **********
        restart: always

    qaeventmq:
        image: daocloud.io/quantaxis/qaeventmq:latest
        ports:
            - "15672:15672"
            - "5672:5672"
            - "4369:4369"
        env_file: env.env
        networks:
            qanetwork_pro:
                ipv4_address: **********
        restart: always


    qa:
        image: daocloud.io/quantaxis/qacommunity:latest
        container_name: qacommunity
        depends_on:
            - mgdb
            - qaeventmq
        networks:
            qanetwork_pro:
                ipv4_address: **********
        ports:
            - "8888:8888"
            - "81:80"
        env_file: env.env
        volumes:
            - qacode:/home
            - qadag:/dag
        restart: always

    # qacron:
    #     image: barretthugh/qa-cron
    #     environment:
    #         - MONGODB=mgdb
    #     restart: always

    qaweb_run:
        image: daocloud.io/quantaxis/qarun:latest
        container_name: qarun
        networks:
            qanetwork_pro:
                ipv4_address: **********
        depends_on:
            - mgdb
            - qaeventmq
        ports:
            - "8010:8010"
        env_file: env.env
        restart: always
        depends_on:
          - qaeventmq
          - mgdb
        command: ['/root/wait_for_it.sh', 'qaeventmq:15672', '--' , "/root/runcelery.sh"]


    qamonitor:
        image: daocloud.io/quantaxis/qa-monitor:latest
        ports:
            - "61209:61209"
            - "61208:61208"
        pid: "host"
        networks:
            qanetwork_pro:
                ipv4_address: **********

        restart: always

    qactpbeebroker:
        image: daocloud.io/quantaxis/qactpbeebroker:latest
        ports:
            - "5000:5000"
        networks:
            qanetwork_pro:
                ipv4_address: **********
        env_file: env.env
        command: ['/root/wait_for_it.sh', 'qaeventmq:15672', '--' , "QACTPBEE", "--userid", "$${CTPBEEuserid}", "--password", '$${CTPBEEpassword}']

    qamarketcollector:
        image: daocloud.io/quantaxis/qarealtimecollector:latest
        ports:
            - "8011:8011"
        depends_on:
            - mgdb
            - qaeventmq
        env_file: env.env
        networks:
            qanetwork_pro:
                ipv4_address: **********
        command:
            ['/root/QUANTAXIS_RealtimeCollector/docker/wait_for_it.sh', 'qaeventmq:5672', '--' ,'/root/QUANTAXIS_RealtimeCollector/docker/start_collector.sh']


    qaclickhouse:
        image: daocloud.io/quantaxis/qa-clickhouse
        ports:
            - "9000:9000"
            - "8123:8123"
            - "9009:9009"
        env_file: env.env
        networks:
            qanetwork_pro:
                ipv4_address: ***********

    qatrader:
        image: daocloud.io/quantaxis/qatrader:latest
        ports:
            - "8020:8020"
        depends_on:
            - mgdb
            - qaeventmq
        env_file: env.env
        command:
            ['/root/QATrader/docker/wait_for_it.sh', 'qaeventmq:5672', '--' ,'qatraderserver']

        networks:
            qanetwork_pro:
                ipv4_address: ***********


    webserver:
        image: puckel/docker-airflow
        restart: always
        depends_on:
            - postgres
            - redis
        env_file: env.env
        volumes:
            - qadag:/usr/local/airflow/dags
            # Uncomment to include custom plugins
            # - ./plugins:/usr/local/airflow/plugins
        ports:
            - "8080:8080"
        command: webserver
        healthcheck:
            test: ["CMD-SHELL", "[ -f /usr/local/airflow/airflow-webserver.pid ]"]
            interval: 30s
            timeout: 30s
            retries: 3

    flower:
        image: puckel/docker-airflow
        restart: always
        depends_on:
            - redis
        env_file: env.env
        ports:
            - "5555:5555"
        command: flower

    scheduler:
        image: puckel/docker-airflow
        restart: always
        depends_on:
            - webserver
        volumes:
            - qadag:/usr/local/airflow/dags
            # Uncomment to include custom plugins
            # - ./plugins:/usr/local/airflow/plugins
        env_file: env.env
        command: scheduler

    worker:
        image: puckel/docker-airflow
        restart: always
        depends_on:
            - scheduler
        volumes:
            - qadag:/usr/local/airflow/dags
            # Uncomment to include custom plugins
            # - ./plugins:/usr/local/airflow/plugins
        env_file: env.env
        command: worker


volumes:
    qamg:
        external:
            name: qamg
    qacode:
        external:
            name: qacode
    pg-data:
            external:
                name: pg-data
    qadag:
            external:
                name: qadag
networks:
    qanetwork_pro:
        ipam:
            config:
                - subnet: **********/24
