"""
分型质量评估器
负责评估市场分型的质量和可靠性
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from ..core.utils import np


class FractalValidator:
    """
    @class FractalValidator
    @brief 分型质量评估器
    @details 负责评估市场分型的质量和可靠性
    """
    
    def __init__(self):
        self.name = "FractalValidator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def validate_fractal_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 验证分型质量
        @param data: 市场数据
        @return: 分型质量评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            price_data = data.get('price_data', {})
            technical_data = data.get('technical_data', {})
            flow_data = data.get('flow_data', {})
            
            # 四个评估维度
            amplitude_score = self._calculate_amplitude_score(price_data)
            volume_score = self._calculate_volume_score(flow_data)
            independence_score = self._calculate_independence_score(technical_data)
            volatility_score = self._calculate_volatility_score(price_data)
            
            # 综合质量评分
            quality_scores = {
                'amplitude_score': amplitude_score,
                'volume_score': volume_score,
                'independence_score': independence_score,
                'volatility_score': volatility_score
            }
            
            overall_quality = np.mean(list(quality_scores.values()))
            quality_level = self._determine_quality_level(overall_quality)
            
            return {
                'fund_code': fund_code,
                'quality_scores': quality_scores,
                'overall_quality': overall_quality,
                'quality_level': quality_level,
                'validation_signals': self._generate_validation_signals(quality_scores),
                'confidence': min(0.95, max(0.3, overall_quality)),
                'validation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Fractal validation failed for {fund_code}: {str(e)}")
            return {
                'fund_code': fund_code,
                'error': str(e),
                'overall_quality': 0.0,
                'quality_level': 'poor'
            }
    
    def _calculate_amplitude_score(self, price_data: Dict[str, Any]) -> float:
        """计算振幅得分"""
        try:
            change_rate = abs(price_data.get('change_rate', 0))
            
            # 振幅评分逻辑
            if change_rate >= 3:
                return min(1.0, change_rate / 5)  # 高振幅
            elif change_rate >= 1:
                return 0.5 + (change_rate - 1) / 4  # 中等振幅
            else:
                return change_rate / 2  # 低振幅
                
        except Exception:
            return 0.0
    
    def _calculate_volume_score(self, flow_data: Dict[str, Any]) -> float:
        """计算成交量得分"""
        try:
            volume = flow_data.get('price_data', {}).get('volume', 0)
            volume_ratio = flow_data.get('volume_analysis', {}).get('current_volume_ratio', 1.0)
            
            # 成交量评分逻辑
            volume_factor = min(1.0, volume / 5000)  # 标准化成交量
            ratio_factor = min(1.0, volume_ratio / 1.5)  # 标准化成交量比率
            
            return (volume_factor + ratio_factor) / 2
            
        except Exception:
            return 0.0
    
    def _calculate_independence_score(self, technical_data: Dict[str, Any]) -> float:
        """计算独立性得分"""
        try:
            rsi = technical_data.get('rsi', 50)
            macd = technical_data.get('macd', 0)
            
            # 独立性评分（基于技术指标的分离度）
            rsi_independence = abs(rsi - 50) / 50  # RSI偏离中性程度
            macd_independence = min(1.0, abs(macd) * 100)  # MACD信号强度
            
            return (rsi_independence + macd_independence) / 2
            
        except Exception:
            return 0.0
    
    def _calculate_volatility_score(self, price_data: Dict[str, Any]) -> float:
        """计算波动性得分"""
        try:
            change_rate = abs(price_data.get('change_rate', 0))
            
            # 波动性评分（适度波动最佳）
            if 1 <= change_rate <= 3:
                return 1.0  # 理想波动范围
            elif change_rate < 1:
                return change_rate  # 波动过小
            else:
                return max(0.3, 1.0 - (change_rate - 3) / 7)  # 波动过大
                
        except Exception:
            return 0.0
    
    def _determine_quality_level(self, overall_quality: float) -> str:
        """确定质量等级"""
        if overall_quality >= 0.8:
            return "excellent"
        elif overall_quality >= 0.6:
            return "good"
        elif overall_quality >= 0.4:
            return "fair"
        elif overall_quality >= 0.2:
            return "poor"
        else:
            return "very_poor"
    
    def _generate_validation_signals(self, quality_scores: Dict[str, float]) -> List[str]:
        """生成验证信号"""
        signals = []
        
        for score_type, score in quality_scores.items():
            if score >= 0.8:
                signals.append(f"{score_type}优秀({score:.2f})")
            elif score >= 0.6:
                signals.append(f"{score_type}良好({score:.2f})")
            elif score >= 0.4:
                signals.append(f"{score_type}一般({score:.2f})")
            else:
                signals.append(f"{score_type}较差({score:.2f})")
        
        return signals
