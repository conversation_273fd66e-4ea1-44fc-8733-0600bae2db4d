
# Self-defined ignore
_data_/
_notebooks_/
*.pyc
*.pyd
node_modules/
.vscode/
*.7z
.idea/
*.log
.vscode/
.DS_Store*
*.pdf
*.gitconfig
*.rdb
.ipython/
.ccls/
.ccls*
.matplotlib/
.log
*.log
.ssh/
*.exe
.pypirc
QA_*.html
test/
*lock.json
docs
record.txt
build/
*/cache/
# Byte-compiled / optimized / DLL files
__pycache__/

*$py.class

#zip
*.zip
# C extensions
*.so
config.toml
# Distribution / packaging
.Python
env/
_build
develop-eggs/
eggs/
.eggs/
lib64/
parts/
*whl
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
*.lnk
manifest
transfer.py
testQ/
# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec
technical_analysis.py
# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/
build/bdist.win32/
build/bdist.win-amd64/
build/lib/
build/bdist.linux-x86_64/
build/lib.linux-x86_64-2.7/

# IPython Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# dotenv
.env

# virtualenv
venv/
ENV/

# Spyder project settings
.spyderproject

# Rope project settings
.ropeproject

# csv,excel,spss
*.sav

*.csv

*.xls
*.hdf5
*.xlsx



#jupyter




# jupyter-kernel

kernel*.json


# packge-lock

.\vs



# sqlite database
*.db


*.dad
*.cover

#future connections

*.con
/QUANTAXIS_Test/QAFetch_Test/data/
*.sqlite_db

selenium_driver/*
QUANTAXIS_WEBDRIVER/*

celerybeat*

/data/

/quantaxis_env/
/huobi_client
/inc/huobi_client
/QUANTAXIS/QAFetch/__main__.py
/QUANTAXIS.pyproj
/QUANTAXIS/QAAnalysis/QABeyond.py
/QUANTAXIS/QAAnalysis/QAAnalysis_kline.py
/QUANTAXIS/.gitignore


# latex

*.aux
*.log
*.out
*.pdf
*.gz
*.toc
.synctex(busy)
*.fdb_latexmk
*.fls