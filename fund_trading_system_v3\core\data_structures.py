"""
核心数据结构定义
包含系统中使用的所有数据类
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Union, Optional, Tuple, Any
from .enums import MarketRegime


@dataclass
class RawBar:
    """原始K线数据结构"""
    symbol: str
    dt: datetime
    id: int
    freq: str
    open: float
    close: float
    high: float
    low: float
    vol: float
    amount: float


@dataclass
class DimensionEvaluationResult:
    """维度评估结果"""
    dimension_name: str
    state: Any  # 对应的枚举状态
    score: float
    confidence: float
    signals: List[str]
    data_quality: str
    details: Optional[Dict[str, Any]] = None
    indicators: Optional[Dict[str, float]] = None


@dataclass
class MultiDimensionalMarketState:
    """多维度市场状态"""
    timestamp: datetime
    trend: DimensionEvaluationResult
    volatility: DimensionEvaluationResult
    liquidity: DimensionEvaluationResult
    sentiment: DimensionEvaluationResult
    structural: DimensionEvaluationResult
    transition: DimensionEvaluationResult
    composite_score: float
    overall_confidence: float
    market_regime: MarketRegime
    recommended_action: str
    risk_level: str


@dataclass
class SignalConflictResult:
    """信号冲突处理结果"""
    final_signal: int  # -1, 0, 1
    final_strength: float
    final_confidence: float
    conflict_reason: str
    resolution_method: str
    contributing_signals: List[Dict[str, Any]]


@dataclass
class DynamicWeights:
    """动态权重结果"""
    trend_weight: float
    volatility_weight: float
    liquidity_weight: float
    sentiment_weight: float
    structural_weight: float
    transition_weight: float
    adjustment_reason: str
    market_conditions: Dict[str, float]


@dataclass
class EnhancedAnalysisResult:
    """增强分析结果"""
    symbol: str
    timestamp: datetime
    market_state: MultiDimensionalMarketState
    signal_conflict_result: SignalConflictResult
    dynamic_weights: DynamicWeights
    composite_score: float
    recommendation: str
    confidence: float
    risk_assessment: Dict[str, Any]
    execution_details: Dict[str, Any]


@dataclass
class TimeframeCycleDivergence:
    """时间周期背驰分析结果"""
    timeframe: str  # 时间框架
    divergence_type: str  # 背驰类型 
    divergence_strength: float  # 背驰强度 (0-1)
    macd_area_ratio: float  # MACD面积比率
    dif_peak_comparison: Dict[str, float]  # DIF峰值比较
    confirmation_bars: int  # 确认K线数量
    cycle_position: str  # 周期位置: early/middle/late


@dataclass  
class MultiTimeframeResonance:
    """多时间框架共振分析结果"""
    short_term: TimeframeCycleDivergence
    medium_term: TimeframeCycleDivergence  
    long_term: TimeframeCycleDivergence
    resonance_score: float  # 共振评分 (0-1)
    consensus_direction: str  # 共识方向: bullish/bearish/neutral
    confidence_level: float  # 置信水平 (0-1)
    transition_signal: str  # 变盘信号强度: strong/medium/weak/none


@dataclass
class CzscBiStructure:
    """缠论笔结构数据"""
    bi_direction: str
    bi_start_price: float
    bi_end_price: float
    bi_amplitude: float
    bi_duration: int
    macd_area: float
    dif_peak: float
