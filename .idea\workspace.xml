<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a0c97ab0-8839-4ee8-a7da-f37506a0fd6f" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/tdx_fetcher.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/chat.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/czsc-master/czsc/connectors/tdx_connector.py" beforeDir="false" afterPath="$PROJECT_DIR$/czsc-master/czsc/connectors/tdx_connector.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/enhanced_soros_agent.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/enhanced_soros_agent.py.bak" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/enhanced_soros_agent2.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/enhanced_soros_agent3.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/enhanced_soros_agent3_v2.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/enhanced_soros_agent4.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/enhanced_soros_example.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2xXlwJIJGj8sTXOhI7fSM730HYB" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/puppet/puppet&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.fonts.default&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\puppet\puppet" />
      <recent name="C:\CzscSystem\puppet" />
      <recent name="C:\CzscSystem" />
      <recent name="C:\ProgramData\Anaconda3\Lib\site-packages\czsc\connectors" />
      <recent name="C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\czsc\connectors" />
    </key>
  </component>
  <component name="RunManager" selected="Python.new_auto_trade_fund_example4_v3">
    <configuration name="enhanced_soros_agent6" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="CzscSystem" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/enhanced_soros_agent6.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="new_auto_trade_fund_example4_v3" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="CzscSystem" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/new_auto_trade_fund_example4_v3.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="enhanced_soros_agent6.py" type="tests" factoryName="py.test" temporary="true">
      <module name="CzscSystem" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;enhanced_soros_agent6.test&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="pytest in enhanced_soros_agent6.py (1)" type="tests" factoryName="py.test" temporary="true" nameIsGenerated="true">
      <module name="CzscSystem" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;$PROJECT_DIR$/enhanced_soros_agent6.py&quot;" />
      <option name="_new_targetType" value="&quot;PATH&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="pytest in enhanced_soros_agent6.py" type="tests" factoryName="py.test" temporary="true" nameIsGenerated="true">
      <module name="CzscSystem" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.new_auto_trade_fund_example4_v3" />
        <item itemvalue="Python.enhanced_soros_agent6" />
        <item itemvalue="Python tests.pytest in enhanced_soros_agent6.py (1)" />
        <item itemvalue="Python tests.enhanced_soros_agent6.py" />
        <item itemvalue="Python tests.pytest in enhanced_soros_agent6.py" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a0c97ab0-8839-4ee8-a7da-f37506a0fd6f" name="Changes" comment="" />
      <created>1748092535409</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748092535409</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>