"""
参数缓存管理器
提供高效的参数缓存机制，减少重复计算
"""

import time
import logging
from typing import Dict, Any, Optional
from ..core.utils import *


class ParameterCacheManager:
    """
    @class ParameterCacheManager
    @brief 参数缓存管理器
    @details 提供高效的参数缓存机制，减少重复计算
    """
    
    def __init__(self, cache_duration: int = 3600):
        """
        @brief 初始化缓存管理器
        @param cache_duration: 缓存持续时间（秒），默认1小时
        """
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_duration = cache_duration
        self.cache_stats = {'hits': 0, 'misses': 0, 'evictions': 0}
    
    def get_cached_params(self, fund_code: str) -> Optional[Dict[str, int]]:
        """获取缓存的参数"""
        try:
            if fund_code in self.cache and self.is_cache_valid(fund_code):
                self.cache_stats['hits'] += 1
                return self.cache[fund_code].copy()
            else:
                self.cache_stats['misses'] += 1
                return None
        except Exception:
            self.cache_stats['misses'] += 1
            return None
    
    def cache_params(self, fund_code: str, params: Dict[str, int]) -> None:
        """缓存参数"""
        try:
            self.cache[fund_code] = params.copy()
            self.cache_timestamps[fund_code] = time.time()
            self.cleanup_expired_cache()
        except Exception as e:
            logging.error(f"参数缓存失败 {fund_code}: {e}")
    
    def is_cache_valid(self, fund_code: str) -> bool:
        """检查缓存是否有效"""
        try:
            if fund_code not in self.cache_timestamps:
                return False
            cache_time = self.cache_timestamps[fund_code]
            current_time = time.time()
            return (current_time - cache_time) < self.cache_duration
        except Exception:
            return False
    
    def cleanup_expired_cache(self) -> None:
        """清理过期缓存"""
        try:
            current_time = time.time()
            expired_keys = []
            
            for fund_code, cache_time in self.cache_timestamps.items():
                if (current_time - cache_time) > self.cache_duration:
                    expired_keys.append(fund_code)
            
            for key in expired_keys:
                if key in self.cache:
                    del self.cache[key]
                if key in self.cache_timestamps:
                    del self.cache_timestamps[key]
                self.cache_stats['evictions'] += 1
        except Exception as e:
            logging.error(f"缓存清理失败: {e}")
    
    def clear_cache(self) -> None:
        """清空所有缓存"""
        self.cache.clear()
        self.cache_timestamps.clear()
        logging.info("All cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = self.cache_stats['hits'] / max(1, total_requests) * 100
        
        return {
            'cache_size': len(self.cache),
            'hit_rate': round(hit_rate, 2),
            'total_requests': total_requests,
            'cache_duration': self.cache_duration,
            'stats': self.cache_stats.copy()
        }
    
    def set_cache_duration(self, duration: int) -> None:
        """设置缓存持续时间"""
        if duration > 0:
            self.cache_duration = duration
            logging.info(f"Cache duration updated to {duration} seconds")
        else:
            logging.warning("Invalid cache duration, keeping current value")
    
    def get_cached_fund_codes(self) -> list:
        """获取所有已缓存的基金代码"""
        return list(self.cache.keys())
    
    def remove_cache(self, fund_code: str) -> bool:
        """移除特定基金的缓存"""
        try:
            if fund_code in self.cache:
                del self.cache[fund_code]
            if fund_code in self.cache_timestamps:
                del self.cache_timestamps[fund_code]
            return True
        except Exception as e:
            logging.error(f"Failed to remove cache for {fund_code}: {e}")
            return False
    
    def get_cache_age(self, fund_code: str) -> Optional[float]:
        """获取缓存年龄（秒）"""
        try:
            if fund_code in self.cache_timestamps:
                return time.time() - self.cache_timestamps[fund_code]
            return None
        except Exception:
            return None
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.cache_stats = {'hits': 0, 'misses': 0, 'evictions': 0}
        logging.info("Cache statistics reset")
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况估算"""
        try:
            import sys
            cache_size = sys.getsizeof(self.cache)
            timestamps_size = sys.getsizeof(self.cache_timestamps)
            total_size = cache_size + timestamps_size
            
            return {
                'cache_size_bytes': cache_size,
                'timestamps_size_bytes': timestamps_size,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'entries_count': len(self.cache)
            }
        except Exception as e:
            logging.error(f"Failed to calculate memory usage: {e}")
            return {'error': str(e)}
