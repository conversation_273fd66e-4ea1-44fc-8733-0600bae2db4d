# QUANTAXIS STU 02 DataFlow 数据流处理 事件驱动

本来说好在P2的内容中详细的讲解如何快速使用QUANTAXIS来构建回测完善你的想法, 但是我觉得还是有必要在一切开始之前 探讨一下quantaxis的核心: 数据流处理


事实上你会发现, 在量化中的一切都是关于事件与数据流的, 我们可以快速梳理一下一切的一切:

> 当我们开始准备量化交易时, 我们会面对

    - 股票/期货的实时价格数据
    - 实时的买卖盘数据
    - 实时的财务数据

    - 基于价格, 成交量的指标数据流
    - 基于指标的信号流
    - 基于信号的订单流

    - 基于价格和持仓的账户动态权益数据流
    - 基于动态权益的绩效风险数据流
    
    - 基于以上数据流的可视化
    - 基于数据流的预测和分析

你会发现, 无论你是日内交易者, 高频交易者, 中长线交易者, 纯量化交易, 手工量化组合交易, ETF套利, FOF基金管理... 一切的一切, 都离不开对于以上的数据流的实时处理能力

所以提纲挈领的讲quantaxis提供解决方案的核心:

- 数据流的处理能力
- 多数据流的组合能力
- 数据流实时可视化的能力


## 如何通俗的理解数据流处理? 举个例子

我们以下一章节要介绍的回测来举例:

1. 首先进行的是数据的获取 QA_fetch_stock_day_adv ==> [拿到DataStruct]

2. 数据在DataStruct的迭代器中被一条一条的推送出来, 形成一个数据流(streaming) [DataStruct.penel_gen]

-------------------------------------------------------

-------------重复这个步骤-------------

==== 以下是策略部分

3. 策略on_bar/tick 收到数据流的一条条数据

    3.1 缓存数据, 方便进行使用了历史数据的指标计算  

    3.2 搭载指标, 计算行情信号   [ind=  Data.add_func(indfunc)]

    3.3 基于账户的持仓/现金/权益/风险, 来判断是否进行下单操作  [Account.send_order]

==== 以下是回测引擎部分


4. 回测引擎收到你的订单, 进行撮合, 调用Account回调 [order.trade]

5. 账户收到回调函数(成交事件) 更新账户的持仓/现金/权益风险  [Account.receive_order]

-------------------------------------------------------

6. 当数据流被完全推送结束, 关闭回测, 进行权益分析/风险分析   [QA_Risk(QA_Account), QA_Performance(QA_Account)]

7. 存储你的整个过程的产物, 方便后续的分析/可视化 [risk.save/performance.save/ QA_Community 界面可视化]


当策略被放入实盘/模拟环境中, 只需要改变的是数据流的来源/ 以及账户的接口 即可切换你的策略环境

## 我们如何基于QUANTAXIS来对于数据流进行处理?

1. 快速(微型)的处理工具: 迭代器

QA_DataStruct 提供的迭代器: 
    panel_gen, secrity_gen

迭代器是python的一个概念, 基于yield, 可以快速在不结束数据流的情况下将数据一条一条的推送出来

如何处理数据:

QADataStruct 提供的批量处理工具 add_func, add_funcx

add_func 函数入口可以快速的在DataStruct上叠加指标(函数), 通过批量的对于多周期多品种的DataStruct叠加函数, 可以快速的对于数据流进行处理


2. 跨进程/分布式的数据流处理能力: messageQueue

基于QAEventMQ + quantaxis_pubsub + qathread 你可以快速的在多个进程/ 多个机器的多个进程中传递数据和事件

支持 广播/ 路由等多种自由的模式


## 如何简单的使用quantaxis提供的数据流工具集?

quantaxis_service 使用docker一键部署, 快速的帮助你在任何机器上搭建起你需要的环境, 帮助你摆脱环境安装配置等困扰, 专注于解决问题的本身

