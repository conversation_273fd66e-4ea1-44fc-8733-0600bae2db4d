# coding:utf-8
#
# The MIT License (MIT)
#
# Copyright (c) 2016-2021 yutiansut/QUANTAXIS
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

import configparser
import json
import os
from multiprocessing import Lock
from QUANTAXIS.QASetting.QALocalize import qa_path, setting_path, strategy_path
from QUANTAXIS.QAUtil.QASql import (
    QA_util_sql_async_mongo_setting,
    QA_util_sql_mongo_setting
)

# quantaxis有一个配置目录存放在 ~/.quantaxis
# 如果配置目录不存在就创建，主要配置都保存在config.json里面
# 貌似yutian已经进行了，文件的创建步骤，他还会创建一个setting的dir
# 需要与yutian讨论具体配置文件的放置位置 author:Will 2018.5.19

DEFAULT_MONGO = os.getenv('MONGODB', 'localhost')
DEFAULT_DB_URI = 'mongodb://{}:27017'.format(DEFAULT_MONGO)
CONFIGFILE_PATH = '{}{}{}'.format(setting_path, os.sep, 'config.ini')
INFO_IP_FILE_PATH = '{}{}{}'.format(setting_path, os.sep, 'info_ip.json')
STOCK_IP_FILE_PATH = '{}{}{}'.format(setting_path, os.sep, 'stock_ip.json')
FUTURE_IP_FILE_PATH = '{}{}{}'.format(setting_path, os.sep, 'future_ip.json')


class QA_Setting():

    def __init__(self, uri=None):
        self.lock = Lock()

        self.mongo_uri = uri or self.get_mongo()
        self.username = None
        self.password = None

        # 加入配置文件地址

    def get_mongo(self):
        config = configparser.ConfigParser()
        if os.path.exists(CONFIGFILE_PATH):
            config.read(CONFIGFILE_PATH)

            try:
                res = config.get('MONGODB', 'uri')
            except:
                res = DEFAULT_DB_URI

        else:
            config = configparser.ConfigParser()
            config.add_section('MONGODB')
            config.set('MONGODB', 'uri', DEFAULT_DB_URI)
            f = open('{}{}{}'.format(setting_path, os.sep, 'config.ini'), 'w')
            config.write(f)
            res = DEFAULT_DB_URI

        return res

    def get_config(
            self,
            section='MONGODB',
            option='uri',
            default_value=DEFAULT_DB_URI
    ):
        """[summary]

        Keyword Arguments:
            section {str} -- [description] (default: {'MONGODB'})
            option {str} -- [description] (default: {'uri'})
            default_value {[type]} -- [description] (default: {DEFAULT_DB_URI})

        Returns:
            [type] -- [description]
        """

        try:
            config = configparser.ConfigParser()
            config.read(CONFIGFILE_PATH)
            return config.get(section, option)
        except:
            res = self.client.quantaxis.usersetting.find_one(
                {'section': section})
            if res:
                return res.get(option, default_value)
            else:
                self.set_config(section, option, default_value)
                return default_value

    def set_config(
            self,
            section='MONGODB',
            option='uri',
            default_value=DEFAULT_DB_URI
    ):
        """[summary]

        Keyword Arguments:
            section {str} -- [description] (default: {'MONGODB'})
            option {str} -- [description] (default: {'uri'})
            default_value {[type]} -- [description] (default: {DEFAULT_DB_URI})

        Returns:
            [type] -- [description]
        """
        t = {'section': section, option: default_value}
        self.client.quantaxis.usersetting.update(
            {'section': section}, {'$set': t}, upsert=True)

        # if os.path.exists(CONFIGFILE_PATH):
        #     config.read(CONFIGFILE_PATH)
        #     self.lock.release()
        #     return self.get_or_set_section(
        #         config,
        #         section,
        #         option,
        #         default_value,
        #         'set'
        #     )

        #     # 排除某些IP
        #     # self.get_or_set_section(config, 'IPLIST', 'exclude', [{'ip': '*******', 'port': 7709}])

        # else:
        #     f = open(CONFIGFILE_PATH, 'w')
        #     config.add_section(section)
        #     config.set(section, option, default_value)

        #     config.write(f)
        #     f.close()
        #     self.lock.release()
        #     return default_value

    def get_or_set_section(
            self,
            config,
            section,
            option,
            DEFAULT_VALUE,
            method='get'
    ):
        """[summary]

        Arguments:
            config {[type]} -- [description]
            section {[type]} -- [description]
            option {[type]} -- [description]
            DEFAULT_VALUE {[type]} -- [description]

        Keyword Arguments:
            method {str} -- [description] (default: {'get'})

        Returns:
            [type] -- [description]
        """

        try:
            if isinstance(DEFAULT_VALUE, str):
                val = DEFAULT_VALUE
            else:
                val = json.dumps(DEFAULT_VALUE)
            if method == 'get':
                return self.get_config(section, option)
            else:
                self.set_config(section, option, val)
                return val

        except:
            self.set_config(section, option, val)
            return val

    def env_config(self):
        return os.environ.get("MONGOURI", None)

    @property
    def client(self):
        return QA_util_sql_mongo_setting(self.mongo_uri)

    @property
    def client_async(self):
        return QA_util_sql_async_mongo_setting(self.mongo_uri)

    def change(self, ip, port):
        self.ip = ip
        self.port = port
        global DATABASE
        DATABASE = self.client
        return self


QASETTING = QA_Setting()
DATABASE = QASETTING.client.quantaxis
DATABASE_ASYNC = QASETTING.client_async.quantaxis


def exclude_from_stock_ip_list(exclude_ip_list):
    """
    explanation:
        从stock_ip_list删除列表exclude_ip_list中的ip,从stock_ip_list删除列表future_ip_list中的ip

    params:
        * exclude_ip_list ->:
            meaning: 需要删除的ip_list
            type: list
            optional: [null]

    return:
        None

    demonstrate:
        Not described

    output:
        Not described
    """
    for exc in exclude_ip_list:
        if exc in stock_ip_list:
            stock_ip_list.remove(exc)

    # 扩展市场
    for exc in exclude_ip_list:
        if exc in future_ip_list:
            future_ip_list.remove(exc)


if os.path.exists(INFO_IP_FILE_PATH):
    with open(INFO_IP_FILE_PATH, "r") as f:
        info_ip_list = json.load(f)
else:
    info_ip_list = [
        {"ip": "*************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "************", "port": 7709},
        {"ip": "************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************0", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "***************", "port": 7709},
        {"ip": "***************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "***********", "port": 7709},
        # added 20190222 from tdx
        {"ip": "*************", "port": 7711, "name": "上海双线资讯主站"},
        {"ip": "*************", "port": 7711, "name": "深圳双线资讯主站1"},
        {"ip": "***************", "port": 7711, "name": "东莞电信资讯主站"},
        {"ip": "*************", "port": 7711, "name": "武汉电信资讯主站2"},
        {"ip": "************", "port": 7711, "name": "武汉电信资讯主站1"},
        {"ip": "***********", "port": 7711, "name": "深圳双线资讯主站2"},
        {"ip": "**************", "port": 7711, "name": "深圳双线资讯主站3"},
        {"ip": "*************", "port": 7711, "name": "深圳双线资讯主站4"},
        {"ip": "*************", "port": 7711, "name": "北京双线资讯主站"},

        {"ip": "***************", "port": 7721},
        {"ip": "**************", "port": 7721},

    ]
    with open(INFO_IP_FILE_PATH, "w") as f:
        json.dump(info_ip_list, f)

if os.path.exists(STOCK_IP_FILE_PATH):
    with open(STOCK_IP_FILE_PATH, "r") as f:
        stock_ip_list = json.load(f)
else:
    stock_ip_list = [
        # added 2022-11-28 from tdx
        {"ip": "**************", "port": 7709, "name": "上海双线主站14"},
        {"ip": "************9", "port": 7709, "name": "武汉电信主站1"},
        {"ip": "*************", "port": 7709, "name": "深圳双线主站7"},
        {"ip": "*************", "port": 7709, "name": "北京双线主站4"},
        {"ip": "************", "port": 7709, "name": "广州双线主站4"},
        {"ip": "************", "port": 7709, "name": "上海双线主站15"},
        {"ip": "*************", "port": 7719, "name": "深圳双线主站8"},
        {"ip": "**************", "port": 7709, "name": "北京双线主站5"},
        {"ip": "*************", "port": 7709, "name": "北京双线主站6"},
        {"ip": "*************", "port": 7709, "name": "北京双线主站7"},
        {"ip": "***************", "port": 7709, "name": "广州双线主站5"},
        {"ip": "***************", "port": 7709, "name": "广州双线主站6"},
        {"ip": "***************", "port": 7709, "name": "广州双线主站7"},
        # added 20190222 from tdx
        {"ip": "*************", "port": 7711, "name": "北京行情主站1"},
        {"ip": "*************", "port": 7709, "name": "深圳行情主站"},
        {"ip": "*************", "port": 7711, "name": "深圳行情主站"},
        {"ip": "*************", "port": 7711, "name": "上海行情主站"},
        {"ip": "***************", "port": 7711, "name": "移动行情主站"},
        {"ip": "***************", "port": 443, "name": "广州行情主站"},
        {"ip": "***************", "port": 80, "name": "广州行情主站"},
        {"ip": "**************", "port": 7711, "name": "杭州行情主站"},
        {"ip": "***************", "port": 7711, "name": "北京行情主站2"},
        # origin
        {"ip": "*************", "port": 7709},  # 北京
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "************", "port": 7709},  # 深圳
        {"ip": "************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},  # 上海
        {"ip": "**************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "***************", "port": 7709},  # 移动
        {"ip": "**************", "port": 7709},  # 广州
        {"ip": "***************", "port": 7709},  # 广州
        {"ip": "************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "**************", "port": 7709},  # 深圳
        {"ip": "**********", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************0", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "**************", "port": 7709},  # 杭州
        {"ip": "**************", "port": 7709},
        {"ip": "************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "***************", "port": 7709},  # 北京
        {"ip": "************", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "***********", "port": 7709},
        {"ip": "*************", "port": 7709},
        {"ip": "*************", "port": 7709},  # 北京
        {"ip": "**************", "port": 7721},
        {"ip": "*************", "port": 7709},  # 上海
        {"ip": "**************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "**************", "port": 7709},
        {"ip": "hq.cjis.cn", "port": 7709},
        {"ip": "hq1.daton.com.cn", "port": 7709},
        {"ip": "jstdx.gtjas.com", "port": 7709},
        {"ip": "shtdx.gtjas.com", "port": 7709},
        {"ip": "sztdx.gtjas.com", "port": 7709},

        {"ip": "***************", "port": 7721},
        {"ip": "**************", "port": 7721},
    ]
    with open(STOCK_IP_FILE_PATH, "w") as f:
        json.dump(stock_ip_list, f)

if os.path.exists(FUTURE_IP_FILE_PATH):
    with open(FUTURE_IP_FILE_PATH, "r") as f:
        future_ip_list = json.load(f)
else:
    future_ip_list = [
        # origin
        {"ip": "*************", "port": 7727, "name": "扩展市场上海双线"},
        {"ip": "*************", "port": 7727, "name": "扩展市场深圳双线1"},
        #{"ip": "***************", "port": 443, "name": "扩展市场东莞主站"},
        {"ip": "**************", "port": 7727, "name": "扩展市场深圳主站"},
        {"ip": "************", "port": 7727, "name": "扩展市场武汉主站1"},
        {"ip": "***********", "port": 7727, "name": "扩展市场深圳双线2"},
        {"ip": "*************", "port": 7721},
        {"ip": "*************", "port": 7721, "name": "华泰证券深圳电信"},
        {"ip": "***************", "port": 7721, "name": "华泰证券云行情"},
        {"ip": "*************", "port": 443, "name": "扩展市场武汉主站2"},
        {"ip": "*************", "port": 7727, "name": "扩展市场北京主站"},
        {"ip": "*************", "port": 7727, "name": "扩展市场武汉主站3"},
        {"ip": "**************", "port": 7727, "name": "扩展市场上海主站1"},
        {"ip": "**************", "port": 7727, "name": "扩展市场上海主站2"},
        # {"ip": "*************","port": 7721},
        # {"ip": "**************", "port": 7721},
        # {"ip": "*************", "port": 7721},
        # added 20190222 from tdx
        {"ip": "**************", "port": 7721, "name": "扩展市场深圳主站"},
        {"ip": "*************", "port": 7727, "name": "扩展市场深圳双线3"},


    ]
    with open(FUTURE_IP_FILE_PATH, "w") as f:
        json.dump(future_ip_list, f)

"""
["**************", "**************", "*************", "***************", "**************", "**************", "************", "************",
"*************", "**************", "*************", "************", "***************"]
"""
